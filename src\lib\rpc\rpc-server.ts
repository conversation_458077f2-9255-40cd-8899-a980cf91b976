import { appRoute } from "@/server/app-route"
import { userRoute } from "@/server/user-route"
import { aiRoute } from "@/server/ai-route"
import { authRoute } from "@/server/auth-route"
import { Hono } from "hono"
import { cors } from "hono/cors"
import { logger } from "hono/logger"

const app = new Hono().basePath("/api")
// Middleware
app.use("*", cors())
app.use("*", logger())

// Routes
export const routes = app
  .route("/", appRoute)
  .route("/user", userRoute)
  .route("/ai", aiRoute)
  .route("/auth", authRoute)

export type AppType = typeof routes
export default app
