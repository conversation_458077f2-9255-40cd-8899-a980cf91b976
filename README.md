# Next.js Full-Stack Application

Ứng dụng web full-stack được xây dựng với Next.js 15, sử dụng Bun, TypeScript, PostgreSQL, và tích hợp AI.

## 🚀 Bắt Đầ<PERSON>

### Yêu <PERSON>u <PERSON>ống

- **Bun** >= 1.0.0 ([Cài đặt Bun](https://bun.sh/docs/installation))
- **Docker** và **Docker Compose** (cho PostgreSQL)
- **Node.js** >= 18 (backup cho Bun)

### 1. Clone và Cài Đặt

```bash
# Clone repository
git clone <repository-url>
cd nextjs-agent

# Cài đặt dependencies với Bun
bun install
```

### 2. Cấu Hình Môi Trường

```bash
# Copy file environment template
cp .env.example .env

# Chỉnh sửa file .env với editor
nano .env
```

**<PERSON><PERSON><PERSON> biến môi trường cần thiết:**

```env
# Database
DATABASE_URL="postgres://postgres:password@localhost:5432/nextjs_agent"
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=nextjs_agent

# App
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# AI Providers (tùy chọn)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# Email (cho auth)
RESEND_API_KEY=your_resend_key
RESEND_FROM_EMAIL=<EMAIL>
```

### 3. Khởi Động Database

```bash
# Khởi động PostgreSQL với Docker
docker compose up -d

# Kiểm tra database đã chạy
docker compose ps
docker compose logs db
```

### 4. Setup Database Schema

```bash
# Chạy migrations
bun db:migrate

# (Tùy chọn) Seed data mẫu
bun run src/lib/db/seed.ts
```

### 5. Chạy Development Server

```bash
# Khởi động development server
bun dev
```

🎉 **Mở trình duyệt tại [http://localhost:3000](http://localhost:3000)**

## 📋 Scripts Quan Trọng

### Development

```bash
bun dev              # Chạy dev server với Turbopack
bun build            # Build production
bun start            # Chạy production server
bun preview          # Preview production build
```

### Database

```bash
bun db:generate      # Tạo migrations từ schema changes
bun db:migrate       # Chạy migrations
bun db:push          # Push schema changes (dev only)
bun db:studio        # Mở Drizzle Studio (GUI database)
bun db:seed          # Seed database với data mẫu
```

### Code Quality

```bash
bun lint             # ESLint check
bun lint:fix         # Fix ESLint errors
bun format           # Format code với Prettier
bun format:check     # Check code formatting
bun typecheck        # TypeScript type checking
```

### Testing & Health Check

```bash
bun test             # Chạy tests (nếu có)
curl http://localhost:3000/api/health  # Health check API
```

## 🏗️ Cấu Trúc Dự Án

```
src/
├── app/             # Next.js App Router
│   ├── api/         # API endpoints (Hono routes)
│   ├── auth/        # Authentication pages
│   └── dashboard/   # Dashboard pages
├── components/      # React components
│   ├── ui/          # shadcn/ui components
│   └── forms/       # Form components
├── lib/             # Utilities & configs
│   ├── db/          # Database (Drizzle ORM)
│   ├── auth.ts      # Better Auth config
│   └── rpc/         # RPC client/server
├── server/          # API route handlers (Hono)
├── hooks/           # Custom React hooks
└── store/           # State management (Zustand)
```

## 🔧 Công Nghệ Sử Dụng

- **Framework**: Next.js 15 với App Router
- **Runtime**: Bun (thay thế Node.js)
- **Language**: TypeScript
- **Database**: PostgreSQL + Drizzle ORM
- **Authentication**: Better Auth
- **API**: Hono (pattern đơn giản)
- **UI**: Tailwind CSS + shadcn/ui
- **State**: Zustand + TanStack Query
- **AI**: Vercel AI SDK

## 🔐 Authentication

### Đăng Ký/Đăng Nhập

- Truy cập `/auth/sign-up` để tạo tài khoản
- Truy cập `/auth/sign-in` để đăng nhập
- Email verification tự động

### Protected Routes

- `/dashboard/*` - Yêu cầu đăng nhập
- Middleware tự động redirect về sign-in

## 🗄️ Database Management

### Xem Database

```bash
# Mở Drizzle Studio (GUI)
bun db:studio
# Truy cập: https://local.drizzle.studio

# Hoặc connect trực tiếp
psql postgres://postgres:password@localhost:5432/nextjs_agent
```

### Schema Changes

```bash
# 1. Sửa file src/lib/db/schema.ts
# 2. Generate migration
bun db:generate
# 3. Review migration file trong drizzle/
# 4. Apply migration
bun db:migrate
```

## 🔌 API Usage

### API Endpoints

- Health check: `GET /api/health`
- Users: `GET /api/user`, `POST /api/user`, `GET /api/user/:id`
- AI Chat: `POST /api/ai/chat`
- Auth: `ALL /api/auth/*` (Better Auth endpoints)

### API Architecture

Sử dụng **Hono** với pattern đơn giản:

- **Simple & Clean**: `const app = new Hono()` - không phức tạp
- **Type-safe**: Export `AppType` cho client type safety
- **Middleware**: CORS, Logger, Auth middleware

### Sử dụng trong React

```typescript
import { useUsers, useCreateUser } from "@/hooks/use-rpc"

function MyComponent() {
  const { data: users } = useUsers()
  const createUser = useCreateUser()

  // ...
}
```

## 🚨 Troubleshooting

### Database Issues

```bash
# Kiểm tra database connection
docker compose ps
docker compose logs db

# Reset database
docker compose down -v
docker compose up -d
bun db:migrate
```

### Build Issues

```bash
# Clear cache
rm -rf .next
bun install --force

# Type check
bun typecheck
```

### Port Conflicts

```bash
# Thay đổi port trong package.json
"dev": "next dev --port 3001"

# Hoặc set environment variable
PORT=3001 bun dev
```

## 📚 Tài Liệu Bổ Sung

- [STRUCTURE.md](./STRUCTURE.md) - Chi tiết cấu trúc dự án
- [RULES.md](./RULES.md) - Quy định development bắt buộc
- [Next.js Documentation](https://nextjs.org/docs)
- [Drizzle ORM Docs](https://orm.drizzle.team/)
- [Better Auth Docs](https://www.better-auth.com/)

## 🤝 Contributing

1. Đọc [RULES.md](./RULES.md) trước khi code
2. Chạy checklist trước commit:
   ```bash
   bun typecheck && bun format && bun lint
   ```
3. Test local trước khi push
4. Tạo PR với mô tả rõ ràng

## 📞 Hỗ Trợ

- **Issues**: Tạo GitHub issue
- **Database**: Kiểm tra `bun db:studio`
- **API**: Test với `/api/health`
- **Logs**: Xem terminal output

---

**🎯 Happy Coding với Bun + Next.js!**
