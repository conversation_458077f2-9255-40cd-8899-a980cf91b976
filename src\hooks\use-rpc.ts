import { rpcClient } from "@/lib/rpc/rpc-client"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

// Users hooks
export function useUsers() {
  return useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const response = await rpcClient.user.$get()
      if (!response.ok) throw new Error("Failed to fetch users")
      return response.json()
    },
  })
}

export function useUser(id: string) {
  return useQuery({
    queryKey: ["users", id],
    queryFn: async () => {
      const response = await rpcClient.user[":id"].$get({
        param: { id },
      })
      if (!response.ok) throw new Error("Failed to fetch user")
      return response.json()
    },
    enabled: !!id,
  })
}

export function useCreateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: { name: string; email: string }) => {
      const response = await rpcClient.user.$post({
        json: data,
      })
      if (!response.ok) throw new Error("Failed to create user")
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] })
    },
  })
}

// Health check query
export const useHealthCheck = () => {
  return useQuery({
    queryKey: ["health"],
    queryFn: async () => {
      const response = await rpcClient.health.$get()
      if (!response.ok) {
        throw new Error("Health check failed")
      }
      return response.json()
    },
  })
}

// AI Chat hook
export function useChatAI() {
  return useMutation({
    mutationFn: async (data: { messages: Array<{ role: string; content: string }> }) => {
      const response = await fetch("/api/ai/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })
      if (!response.ok) throw new Error("Failed to chat with AI")
      return response.text()
    },
  })
}
