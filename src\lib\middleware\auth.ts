import { auth } from "@/lib/auth"
import type { Context, Next } from "hono"

export const authMiddleware = async (c: Context, next: Next) => {
  const authHeader = c.req.header("Authorization")
  if (!authHeader?.startsWith("Bearer ")) {
    return c.json({ error: "Unauthorized" }, 401)
  }

  const token = authHeader.split(" ")[1]
  const session = await auth.api.getSession({
    headers: new Headers({ cookie: `better-auth.session_token=${token}` }),
  })

  if (!session) {
    return c.json({ error: "Invalid session" }, 401)
  }

  c.set("user", session.user)
  c.set("session", session.session)
  await next()
}