import { auth } from "@/lib/auth"
import { ROUTER } from "@/router"
import { headers } from "next/headers"
import { redirect } from "next/navigation"

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await auth.api.getSession({
    headers: await headers(),
  })

  if (!session) {
    redirect(ROUTER.signin)
  }

  return <div>{children}</div>
}
