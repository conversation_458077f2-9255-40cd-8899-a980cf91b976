"use client"

import { useEffect, useRef, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useTypingStore } from "@/store/useTypingStore"
import { Card } from "@/components/ui/card"

export default function Terminal() {
  const {
    currentLine,
    history,
    caretPosition,
    isWaitingForResponse,
    setIsTyping,
  } = useTypingStore()

  const [showCaret, setShowCaret] = useState(true)
  const scrollRef = useRef<HTMLDivElement>(null)

  // Caret blinking effect
  useEffect(() => {
    const interval = setInterval(() => {
      setShowCaret(prev => !prev)
    }, 500)
    return () => clearInterval(interval)
  }, [])

  // Auto scroll to bottom when new content is added
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight
    }
  }, [history, currentLine])

  // Set typing status
  useEffect(() => {
    setIsTyping(currentLine.length > 0)
  }, [currentLine, setIsTyping])

  const renderCurrentLineWithCaret = () => {
    const beforeCaret = currentLine.substring(0, caretPosition)
    const afterCaret = currentLine.substring(caretPosition)

    return (
      <span className="flex">
        <span className="text-green-400">$ </span>
        <span>
          {beforeCaret}
          <span
            className={`inline-block w-2 h-5 bg-green-400 ${showCaret ? "opacity-100" : "opacity-0"} transition-opacity duration-100`}
          >
            {/* Caret */}
          </span>
          {afterCaret}
        </span>
      </span>
    )
  }

  return (
    <Card className="h-full bg-gray-900/95 border-gray-700 overflow-hidden">
      {/* Terminal Header */}
      <div className="flex items-center px-4 py-3 bg-gray-800 border-b border-gray-700">
        <div className="flex space-x-2">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
        </div>
        <div className="flex-1 text-center">
          <span className="text-sm text-gray-400 font-mono">Terminal</span>
        </div>
      </div>

      {/* Terminal Content */}
      <div
        ref={scrollRef}
        className="flex-1 p-4 font-mono text-sm overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800"
        style={{
          height: "calc(100% - 60px)",
          maxHeight: "400px", // Giới hạn chiều cao trên mobile
        }}
      >
        {/* History */}
        <AnimatePresence>
          {history.map((line, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2, delay: index * 0.05 }}
              className={`mb-2 ${
                line.startsWith("$")
                  ? "text-green-400"
                  : line.startsWith("🤖 AI:")
                    ? "text-blue-300"
                    : line.startsWith("Welcome") || line.includes("🚀")
                      ? "text-blue-400"
                      : "text-gray-300"
              }`}
            >
              {line}
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Current Line */}
        <div className="text-green-400 flex items-center">
          {renderCurrentLineWithCaret()}
        </div>

        {/* Waiting for response indicator */}
        {isWaitingForResponse && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-blue-400 flex items-center mt-2"
          >
            <span className="mr-2">🤖 AI is thinking</span>
            <div className="flex space-x-1">
              <motion.div
                className="w-2 h-2 bg-blue-400 rounded-full"
                animate={{ y: [-2, 2, -2] }}
                transition={{ repeat: Infinity, duration: 0.6, delay: 0 }}
              />
              <motion.div
                className="w-2 h-2 bg-blue-400 rounded-full"
                animate={{ y: [-2, 2, -2] }}
                transition={{ repeat: Infinity, duration: 0.6, delay: 0.2 }}
              />
              <motion.div
                className="w-2 h-2 bg-blue-400 rounded-full"
                animate={{ y: [-2, 2, -2] }}
                transition={{ repeat: Infinity, duration: 0.6, delay: 0.4 }}
              />
            </div>
          </motion.div>
        )}
      </div>
    </Card>
  )
}
