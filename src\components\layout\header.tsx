"use client"

import Link from "next/link"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { signOut, useSession } from "@/lib/auth-client"
import { ROUTER } from "@/router"

export function Header() {
  const { data: session, isPending } = useSession()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut()
    router.push("/")
    router.refresh()
  }

  return (
    <header className="border-b bg-white">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link href="/" className="text-xl font-bold text-blue-600">
          MyApp
        </Link>

        <nav className="flex items-center space-x-4">
          {isPending ? (
            <div>Loading...</div>
          ) : session?.user ? (
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="text-gray-700 hover:text-blue-600"
              >
                Dashboard
              </Link>
              <span className="text-gray-600">
                <PERSON><PERSON> chà<PERSON>, {session.user.name}
              </span>
              <Button variant="ghost" onClick={handleSignOut}>
                Đăng xuất
              </Button>
            </div>
          ) : (
            <div className="flex space-x-2">
              <Link href={ROUTER.signin}>
                <Button variant="ghost">Đăng nhập</Button>
              </Link>
              <Link href={ROUTER.signup}>
                <Button>Đăng ký</Button>
              </Link>
            </div>
          )}
        </nav>
      </div>
    </header>
  )
}
