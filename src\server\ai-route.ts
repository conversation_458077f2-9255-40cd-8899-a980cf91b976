import { streamText } from "ai"
import { openai } from "@/lib/ai/ai-config"
import { authMiddleware } from "@/lib/middleware/auth"
import { Hono } from "hono"

export const aiRoute = new Hono()
  .post("/chat", authMiddleware, async c => {
    try {
      const { messages } = await c.req.json()

      const result = streamText({
        model: openai("gpt-4-turbo-preview"),
        messages,
        temperature: 0.7,
        system: "You are a helpful assistant.",
      })

      return result.toTextStreamResponse()
    } catch (error) {
      console.error("AI Chat Error:", error)
      return c.json({ error: "Internal Server Error" }, 500)
    }
  })