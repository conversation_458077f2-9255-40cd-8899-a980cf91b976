import { auth } from "@/lib/auth"
import { headers } from "next/headers"

export default async function DashboardPage() {
  const session = await auth.api.getSession({
    headers: await headers(),
  })

  return (
    <div>
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Dashboard</h1>
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">
          Chào mừng, {session?.user?.name}!
        </h2>
        <p className="text-gray-600">
          Đây là trang dashboard của bạn. Bạn có thể quản lý các tính năng của
          ứng dụng từ đây.
        </p>
      </div>
    </div>
  )
}
