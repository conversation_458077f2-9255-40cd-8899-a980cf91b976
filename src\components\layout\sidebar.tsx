"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

const navigation = [
  { name: "Dashboard", href: "/dashboard" },
  { name: "Posts", href: "/dashboard/posts" },
  { name: "Users", href: "/dashboard/users" },
  { name: "AI Chat", href: "/dashboard/chat" },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 bg-gray-50 border-r min-h-screen">
      <div className="p-4">
        <h2 className="text-lg font-semibold text-gray-800">Menu</h2>
        <nav className="mt-4 space-y-1">
          {navigation.map(item => (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "block px-3 py-2 rounded-md text-sm font-medium transition-colors",
                pathname === item.href
                  ? "bg-blue-100 text-blue-700"
                  : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
              )}
            >
              {item.name}
            </Link>
          ))}
        </nav>
      </div>
    </div>
  )
}
