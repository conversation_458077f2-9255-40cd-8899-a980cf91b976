"use client"

import { useEffect, useRef, useState } from "react"
import { Keyboard } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useTypingStore } from "@/store/useTypingStore"

interface KeyboardInputProps {
  className?: string
}

export default function KeyboardInput({ className }: KeyboardInputProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const [isMobile, setIsMobile] = useState(false)
  const [showMobileButton, setShowMobileButton] = useState(false)

  const {
    handleKeyPress,
    handleBackspace,
    handleEnter,
    handleArrowLeft,
    handleArrowRight,
    addPressedKey,
    removePressedKey,
    clearPressedKeys,
  } = useTypingStore()

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const mobile =
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent
        ) ||
        "ontouchstart" in window ||
        window.innerWidth <= 768
      setIsMobile(mobile)
      setShowMobileButton(mobile)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)
    return () => window.removeEventListener("resize", checkMobile)
  }, [])

  // Auto focus input when component mounts
  useEffect(() => {
    if (!isMobile && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isMobile])

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      event.preventDefault()

      const { key, code, shiftKey } = event

      // Add pressed key for animation
      if (code) {
        addPressedKey(code)
      }

      // Handle special keys
      switch (code) {
        case "Backspace":
          handleBackspace()
          break
        case "Enter":
          handleEnter()
          break
        case "ArrowLeft":
          handleArrowLeft()
          break
        case "ArrowRight":
          handleArrowRight()
          break
        case "Space":
          handleKeyPress(" ", "Space", shiftKey)
          break
        default:
          // Handle regular keys
          if (key.length === 1) {
            handleKeyPress(key, code, shiftKey)
          }
          break
      }
    }

    const handleKeyUp = (event: KeyboardEvent) => {
      const { code } = event
      if (code) {
        removePressedKey(code)
      }
    }

    const handleBlur = () => {
      clearPressedKeys()
    }

    // Add event listeners to window for global key capture
    window.addEventListener("keydown", handleKeyDown)
    window.addEventListener("keyup", handleKeyUp)
    window.addEventListener("blur", handleBlur)

    return () => {
      window.removeEventListener("keydown", handleKeyDown)
      window.removeEventListener("keyup", handleKeyUp)
      window.removeEventListener("blur", handleBlur)
    }
  }, [
    handleKeyPress,
    handleBackspace,
    handleEnter,
    handleArrowLeft,
    handleArrowRight,
    addPressedKey,
    removePressedKey,
    clearPressedKeys,
  ])

  const focusInput = () => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }

  return (
    <div className={className}>
      {/* Hidden input for capturing keyboard events */}
      <input
        ref={inputRef}
        type="text"
        className="fixed -left-full -top-full opacity-0 pointer-events-none"
        autoComplete="off"
        autoCorrect="off"
        autoCapitalize="off"
        spellCheck={false}
        aria-hidden="true"
        tabIndex={-1}
      />

      {/* Mobile keyboard button */}
      {showMobileButton && (
        <div className="fixed bottom-4 right-4 z-50">
          <Button
            onClick={focusInput}
            size="lg"
            className="rounded-full w-14 h-14 shadow-lg"
            aria-label="Open keyboard"
          >
            <Keyboard size={24} />
          </Button>
        </div>
      )}
    </div>
  )
}
