import { betterAuth } from "better-auth"
import { oneTap, organization } from "better-auth/plugins"
import { drizzleAdapter } from "better-auth/adapters/drizzle"
import { env } from "@/env"
import { db } from "@/lib/db/drizzle"
import { resend, resendFrom } from "@/lib/email/resend"
import { reactResetPasswordEmail } from "@/lib/email/reset-password"
import { reactInvitationEmail } from "@/lib/email/invitation"
import { nextCookies } from "better-auth/next-js"

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
    async sendResetPassword({ user, url }) {
      await resend.emails.send({
        from: resendFrom,
        to: user.email,
        subject: "Reset your password",
        react: reactResetPasswordEmail({
          username: user.email,
          resetLink: url,
        }),
      })
    },
  },
  emailVerification: {
    async sendVerificationEmail({ user, url }) {
      const res = await resend.emails.send({
        from: resendFrom,
        to: user.email,
        subject: "Verify your email address",
        html: `<a href="${url}">Verify your email address</a>`,
      })
      console.log(res, user.email)
    },
  },
  // socialProviders: {
  //     google: {
  //         clientId: env.GOOGLE_CLIENT_ID || '',
  //         clientSecret: env.GOOGLE_CLIENT_SECRET || '',
  //     },
  // },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 24 hours
  },
  user: {
    // additionalFields: {
    //     role: {
    //         type: 'string',
    //         defaultValue: 'user',
    //     },
    // },
  },
  plugins: [
    organization({
      async sendInvitationEmail(data) {
        await resend.emails.send({
          from: resendFrom,
          to: data.email,
          subject: "You've been invited to join an organization",
          react: reactInvitationEmail({
            username: data.email,
            invitedByUsername: data.inviter.user.name,
            invitedByEmail: data.inviter.user.email,
            teamName: data.organization.name,
            inviteLink: `${env.NEXT_PUBLIC_APP_URL}/accept-invitation/${data.id}`,
          }),
        })
      },
    }),
    oneTap(),
    nextCookies(),
  ],
  advanced: {
    crossSubDomainCookies: {
      enabled: false,
    },
  },
})

export type Session = typeof auth.$Infer.Session
export type User = typeof auth.$Infer.Session.user
