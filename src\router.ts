export const ROUTER = {
  errorPermission: "/error-permission",
  signin: "/auth/sign-in",
  signup: "/auth/sign-up",
  profile: "/profile",
  adminUser: "/admin/user",
  adminSetting: "/admin/setting",
  verifyEmail: "/auth/new-verification",
  dashboard: "/dashboard",
  organizationCreate: "/organization",
  organization: "/dashboard/organization",
  connect: "/dashboard/connect",
  connectJob: "/dashboard/connect/job",
  connectThirdparty: "/dashboard/connect/thirdparty",
  notification: "/dashboard/notification",
  order: "/dashboard/order",
  orderDetail: "/dashboard/order/detail",
  orderCreate: "/dashboard/order/create",
  orderShipingDocument: "/order/shiping-document",
  orderPrints: "/order/prints",
}

// * an array of roues that are public
// * These roues do not require authentication
// @ @type {string[]}
export const protectedRoutes = ["/dashboard"]

// * an array of roues that are used for authentication
// * These routes will redirect logged in users to /settings
// @ @type {string[]}
export const authRoutes = ["/auth"]

/**
 * The default redirect path after logging in
 * @type {string}
 */
export const DEFAULT_LOGIN_REDIRECT = ROUTER.signin
export const DEFAULT_AUTH_REDIRECT = "/auth"
export const DEFAULT_ORGANIZATION_REDIRECT = "/organization"
export const DEFAULT_ROLE_PERMISSION_REDIRECT = "/error-permission"
