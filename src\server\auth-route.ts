import { auth } from "@/lib/auth"
import { Hono } from "hono"

export const authRoute = new Hono().all("/*", async c => {
  // Forward all auth requests to better-auth
  const request = new Request(c.req.url, {
    method: c.req.method,
    headers: c.req.header(),
    body:
      c.req.method !== "GET" && c.req.method !== "HEAD"
        ? await c.req.arrayBuffer()
        : undefined,
  })

  const response = await auth.handler(request)

  // Convert Response to Hono response
  const body = await response.text()
  return new Response(body, {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers,
  })
})
