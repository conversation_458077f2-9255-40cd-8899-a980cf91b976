// import { useChat, useCompletion } from '@ai-sdk/react';
// import { useSession } from '@/lib/auth-client';

// export function useAIChat() {
//     const { data: session } = useSession();

//     return useChat({
//         api: '/api/ai/chat',
//         headers: {
//             Authorization: `Bearer ${session?.session?.token ?? ''}`,
//         },
//         onError: (error: unknown) => {
//             console.error('Chat error:', error);
//         },
//     });
// }

// export function useAICompletion() {
//     const { data: session } = useSession();

//     return useCompletion({
//         api: '/api/ai/completion',
//         headers: {
//             Authorization: `Bearer ${session?.session?.token ?? ''}`,
//         },
//     });
// }
