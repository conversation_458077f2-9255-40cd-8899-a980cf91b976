// import { z } from 'zod';

// /**
//  * Server-only schema (validated only on server)
//  */
// const serverEnvSchema = z.object({
//     NODE_ENV: z.string(),
//     DATABASE_URL: z.url(),
//     BETTER_AUTH_SECRET: z.string().min(32),
//     BETTER_AUTH_URL: z.url(),
//     GOOGLE_CLIENT_ID: z.string().optional(),
//     GOOGLE_CLIENT_SECRET: z.string().optional(),
//     OPENAI_API_KEY: z.string().optional(),
//     ANTHROPIC_API_KEY: z.string().optional(),
//     RESEND_API_KEY: z.string().optional(),
//     BETTER_AUTH_EMAIL: z.string().optional(),
// });

// /**
//  * Client-safe schema (validated on client and server)
//  */
// const clientEnvSchema = z.object({
//     NEXT_PUBLIC_APP_URL: z.url(),
// });

// let serverEnv = {};
// let clientEnv = { NEXT_PUBLIC_APP_URL: '' };

// // Logging to help debug ZodError in client bundle
// if (typeof window === 'undefined') {
//     // running on server — validate full server schema and client schema
//     const parsedServer = serverEnvSchema.safeParse(process.env);
//     if (!parsedServer.success) {
//         console.error('Server env validation failed:', parsedServer.error);
//         throw parsedServer.error;
//     }
//     serverEnv = parsedServer.data;
//     clientEnv = clientEnvSchema.parse(process.env);
// } else {
//     // running on client — avoid throwing to prevent client runtime crash
//     console.log('src/lib/env.ts: running on client. typeof window =', typeof window);
//     const publicKeys = Object.keys(process.env || {}).filter((k) => k.startsWith('NEXT_PUBLIC'));
//     console.log('src/lib/env.ts: public env keys available:', publicKeys);
//     console.log('src/lib/env.ts: NEXT_PUBLIC_APP_URL (build-time):', process.env.NEXT_PUBLIC_APP_URL);
//     const parsedClient = clientEnvSchema.safeParse(process.env);
//     if (!parsedClient.success) {
//         // non-fatal on client — surface details for debugging
//         console.error('Client env validation failed (non-fatal):', parsedClient.error);
//     } else {
//         clientEnv = parsedClient.data;
//     }
// }

// export const env = {
//     // server fields may be undefined on client
//     ...(serverEnv as Record<string, unknown>),
//     ...(clientEnv as { NEXT_PUBLIC_APP_URL: string }),
// } as {
//     NODE_ENV: string,
//     DATABASE_URL?: string;
//     BETTER_AUTH_SECRET?: string;
//     BETTER_AUTH_URL?: string;
//     GOOGLE_CLIENT_ID?: string;
//     GOOGLE_CLIENT_SECRET?: string;
//     OPENAI_API_KEY?: string;
//     ANTHROPIC_API_KEY?: string;
//     NEXT_PUBLIC_APP_URL: string;
//     RESEND_API_KEY?: string;
//     BETTER_AUTH_EMAIL?: string;
// };

export const env = {
  NODE_ENV: process.env.NODE_ENV,
  DATABASE_URL: process.env.DATABASE_URL,
  BETTER_AUTH_SECRET: process.env.BETTER_AUTH_SECRET,
  BETTER_AUTH_URL: process.env.BETTER_AUTH_URL,
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
  OPENAI_API_KEY: process.env.OPENAI_API_KEY,
  ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,
  NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
  RESEND_API_KEY: process.env.RESEND_API_KEY,
  BETTER_AUTH_EMAIL: process.env.BETTER_AUTH_EMAIL,

  //   MOCK_SEND_EMAIL:
  //     process.env.MOCK_SEND_EMAIL === "true" ||
  //     process.env.MOCK_SEND_EMAIL === "1",
  //   RESEND_API_KEY: process.env.RESEND_API_KEY,
  //   // SMTP_HOST: process.env.SMTP_HOST,
  //   // SMTP_PORT: parseInt(process.env.SMTP_PORT ?? ''),
  //   // SMTP_USER: process.env.SMTP_USER,
  //   // SMTP_PASSWORD: process.env.SMTP_PASSWORD,

  //   SHOPEE_ENV: process.env.SHOPEE_ENV,
  //   SHOPEE_PARTNER_KEY: process.env.SHOPEE_PARTNER_KEY,
  //   SHOPEE_PARTNER_ID: process.env.SHOPEE_PARTNER_ID,

  //   NEXT_PUBLIC_ENV: process.env.NEXT_PUBLIC_ENV,
  //   VERSION: packageInfo.version,
}
