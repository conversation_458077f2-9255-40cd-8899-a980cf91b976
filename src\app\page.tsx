"use client"

import Keyboard3D from "@/app/_component/Keyboard3D"
import KeyboardInput from "@/app/_component/KeyboardInput"
import Terminal from "@/app/_component/Terminal"
import { Button } from "@/components/ui/button"
import { signOut, useSession } from "@/lib/auth-client"
import { ROUTER } from "@/router"
import Link from "next/link"
import { useRouter } from "next/navigation"

export default function Home() {
  const { data: session, isPending } = useSession()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut()
    router.push("/")
    router.refresh()
  }
  return (
    <div>
      <main className="h-screen flex flex-col items-center">
        <header className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border ">
          <div className="container mx-auto px-4 h-16 flex items-center justify-between max-w-5xl">
            {/* Logo */}
            <Link
              href="/"
              className="text-2xl font-bold text-foreground hover:text-primary transition-colors"
            >
              GG AI
            </Link>

            {/* Get Started Button */}
            {isPending ? (
              <div>Loading...</div>
            ) : session?.user ? (
              <div className="flex items-center space-x-4">
                <Link
                  href="/dashboard"
                  className="text-gray-700 hover:text-blue-600"
                >
                  Dashboard
                </Link>
                <span className="text-gray-600">
                  Xin chào, {session.user.name}
                </span>
                <Button variant="ghost" onClick={handleSignOut}>
                  Đăng xuất
                </Button>
              </div>
            ) : (
              <div className="flex space-x-2">
                <Link href={ROUTER.signin}>
                  <Button variant="ghost">Đăng nhập</Button>
                </Link>
                <Link href={ROUTER.signup}>
                  <Button>Đăng ký</Button>
                </Link>
              </div>
            )}
          </div>
        </header>
        {/* Hero Section */}
        <section className="flex-1 flex flex-col max-w-5xl pt-20 ">
          {/* Terminal Panel */}
          <div className="flex-1 ">
            <div className="h-full px-2 md:px-0">
              <Terminal />
            </div>
          </div>

          {/* Keyboard 3D Panel */}
          <div className="flex-1 ">
            <Keyboard3D />
            <KeyboardInput className="absolute inset-0" />
          </div>
        </section>
      </main>
    </div>
  )
}
