"use client"

import { motion } from "framer-motion"
import { useTypingStore } from "@/store/useTypingStore"

interface KeyboardProps {
  className?: string
}

interface KeyProps {
  children: React.ReactNode
  code: string
  className?: string
  size?: "sm" | "md" | "lg" | "xl"
  onClick?: () => void
}

const Key = ({
  children,
  code,
  className = "",
  size = "md",
  onClick,
}: KeyProps) => {
  const { pressedKeys } = useTypingStore()
  const isPressed = pressedKeys.has(code)

  const handleClick = () => {
    console.log("🔥 Key button clicked:", code)
    onClick?.()
  }

  const sizeClasses = {
    sm: "min-w-6 h-8 md:min-w-10 lg:min-w-12 xl:min-w-12 md:h-10 lg:h-12 text-xs md:text-sm lg:text-base",
    md: "min-w-7 h-8 md:min-w-12 lg:min-w-14 xl:min-w-14 md:h-10 lg:h-12 text-xs md:text-sm lg:text-base",
    lg: "min-w-10 h-8 md:min-w-16 lg:min-w-20 xl:min-w-20 md:h-10 lg:h-12 text-xs md:text-sm lg:text-base",
    xl: "min-w-12 h-8 md:min-w-20 lg:min-w-24 xl:min-w-24 md:h-10 lg:h-12 text-xs md:text-sm lg:text-base",
  }

  return (
    <motion.button
      className={`
        ${sizeClasses[size]}
        ${
          isPressed
            ? "bg-blue-500 text-white shadow-inner transform scale-95"
            : "bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 shadow-md hover:shadow-lg"
        }
        border border-gray-300 dark:border-gray-600
        rounded-md font-medium transition-all duration-150
        flex items-center justify-center
        select-none cursor-pointer active:scale-95
        pointer-events-auto relative z-10
        ${className}
      `}
      onClick={handleClick}
      animate={isPressed ? { scale: 0.95, y: 1 } : { scale: 1, y: 0 }}
      transition={{ duration: 0.1 }}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.95 }}
    >
      {children}
    </motion.button>
  )
}

const KeyboardRow = ({
  children,
  className = "",
}: {
  children: React.ReactNode
  className?: string
}) => (
  <div
    className={`flex gap-0.5 md:gap-1.5 lg:gap-2 justify-center ${className}`}
  >
    {children}
  </div>
)

export default function Keyboard3D({ className }: KeyboardProps) {
  const {
    handleKeyPress,
    handleBackspace,
    handleEnter,
    handleTab,
    handleSpecialKey,
    addPressedKey,
    removePressedKey,
  } = useTypingStore()

  // Handle key press simulation
  const handleKeyClick = (key: string, code: string, shiftKey = false) => {
    console.log("🖱️ Key clicked:", key, code)
    addPressedKey(code)

    // Handle special keys
    switch (key) {
      case "Backspace":
        handleBackspace()
        break
      case "Enter":
        handleEnter()
        break
      case "Tab":
        handleTab()
        break
      case "CapsLock":
      case "Escape":
        handleSpecialKey(key)
        break
      case "Shift":
      case "Control":
      case "Alt":
      case "Meta":
        // Handle modifier keys - just visual feedback for now
        console.log(`${key} pressed`)
        break
      case " ":
        // Handle space
        handleKeyPress(" ", code, shiftKey)
        break
      default:
        // Handle regular character keys and function keys
        if (key.startsWith("F") && key.length <= 3) {
          // Function keys F1-F12
          handleSpecialKey(key)
        } else {
          // Regular characters and symbols
          handleKeyPress(key, code, shiftKey)
        }
        break
    }

    // Release key after animation
    setTimeout(() => {
      removePressedKey(code)
    }, 150)
  }

  return (
    <div className={`w-full h-full ${className}`}>
      <div className="w-full h-full flex flex-col justify-center items-center px-2 md:px-0">
        {/* Keyboard Layout */}
        <div className="relative w-full max-w-sm md:max-w-4xl lg:max-w-none overflow-x-auto">
          {/* Keyboard Base Background */}
          <div className="absolute inset-0 bg-gradient-to-b from-gray-800 to-gray-900 dark:from-gray-900 dark:to-black rounded-xl md:rounded-2xl shadow-lg md:shadow-2xl border border-gray-700 dark:border-gray-800"></div>

          {/* Keyboard Keys Container */}
          <div className="relative space-y-1 md:space-y-4 p-2 md:p-6 lg:p-8 w-full min-w-fit">
            {/* Function Row - Desktop only */}
            <KeyboardRow className="hidden lg:flex">
              <Key
                code="Escape"
                size="md"
                onClick={() => handleKeyClick("Escape", "Escape")}
              >
                Esc
              </Key>
              <div className="w-4 md:w-6"></div> {/* Gap */}
              <Key
                code="F1"
                size="md"
                onClick={() => handleKeyClick("F1", "F1")}
              >
                F1
              </Key>
              <Key
                code="F2"
                size="md"
                onClick={() => handleKeyClick("F2", "F2")}
              >
                F2
              </Key>
              <Key
                code="F3"
                size="md"
                onClick={() => handleKeyClick("F3", "F3")}
              >
                F3
              </Key>
              <Key
                code="F4"
                size="md"
                onClick={() => handleKeyClick("F4", "F4")}
              >
                F4
              </Key>
              <div className="w-2 md:w-3"></div>
              <Key
                code="F5"
                size="md"
                onClick={() => handleKeyClick("F5", "F5")}
              >
                F5
              </Key>
              <Key
                code="F6"
                size="md"
                onClick={() => handleKeyClick("F6", "F6")}
              >
                F6
              </Key>
              <Key
                code="F7"
                size="md"
                onClick={() => handleKeyClick("F7", "F7")}
              >
                F7
              </Key>
              <Key
                code="F8"
                size="md"
                onClick={() => handleKeyClick("F8", "F8")}
              >
                F8
              </Key>
              <div className="w-2 md:w-3"></div>
              <Key
                code="F9"
                size="md"
                onClick={() => handleKeyClick("F9", "F9")}
              >
                F9
              </Key>
              <Key
                code="F10"
                size="md"
                onClick={() => handleKeyClick("F10", "F10")}
              >
                F10
              </Key>
              <Key
                code="F11"
                size="md"
                onClick={() => handleKeyClick("F11", "F11")}
              >
                F11
              </Key>
              <Key
                code="F12"
                size="md"
                onClick={() => handleKeyClick("F12", "F12")}
              >
                F12
              </Key>
            </KeyboardRow>

            {/* Mobile Simple Number Row */}
            <KeyboardRow className="flex md:hidden">
              <Key
                code="Digit1"
                size="sm"
                onClick={() => handleKeyClick("1", "Digit1")}
              >
                1
              </Key>
              <Key
                code="Digit2"
                size="sm"
                onClick={() => handleKeyClick("2", "Digit2")}
              >
                2
              </Key>
              <Key
                code="Digit3"
                size="sm"
                onClick={() => handleKeyClick("3", "Digit3")}
              >
                3
              </Key>
              <Key
                code="Digit4"
                size="sm"
                onClick={() => handleKeyClick("4", "Digit4")}
              >
                4
              </Key>
              <Key
                code="Digit5"
                size="sm"
                onClick={() => handleKeyClick("5", "Digit5")}
              >
                5
              </Key>
              <Key
                code="Digit6"
                size="sm"
                onClick={() => handleKeyClick("6", "Digit6")}
              >
                6
              </Key>
              <Key
                code="Digit7"
                size="sm"
                onClick={() => handleKeyClick("7", "Digit7")}
              >
                7
              </Key>
              <Key
                code="Digit8"
                size="sm"
                onClick={() => handleKeyClick("8", "Digit8")}
              >
                8
              </Key>
              <Key
                code="Digit9"
                size="sm"
                onClick={() => handleKeyClick("9", "Digit9")}
              >
                9
              </Key>
              <Key
                code="Digit0"
                size="sm"
                onClick={() => handleKeyClick("0", "Digit0")}
              >
                0
              </Key>
              <Key
                code="Backspace"
                size="sm"
                onClick={() => handleKeyClick("Backspace", "Backspace")}
              >
                ⌫
              </Key>
            </KeyboardRow>

            {/* Desktop Number Row */}
            <KeyboardRow className="hidden md:flex">
              <Key
                code="Backquote"
                onClick={() => handleKeyClick("`", "Backquote")}
              >
                <div className="flex flex-col text-xs">
                  <span>~</span>
                  <span>`</span>
                </div>
              </Key>
              <Key code="Digit1" onClick={() => handleKeyClick("1", "Digit1")}>
                <div className="flex flex-col text-xs">
                  <span>!</span>
                  <span>1</span>
                </div>
              </Key>
              <Key code="Digit2" onClick={() => handleKeyClick("2", "Digit2")}>
                <div className="flex flex-col text-xs">
                  <span>@</span>
                  <span>2</span>
                </div>
              </Key>
              <Key code="Digit3" onClick={() => handleKeyClick("3", "Digit3")}>
                <div className="flex flex-col text-xs">
                  <span>#</span>
                  <span>3</span>
                </div>
              </Key>
              <Key code="Digit4" onClick={() => handleKeyClick("4", "Digit4")}>
                <div className="flex flex-col text-xs">
                  <span>$</span>
                  <span>4</span>
                </div>
              </Key>
              <Key code="Digit5" onClick={() => handleKeyClick("5", "Digit5")}>
                <div className="flex flex-col text-xs">
                  <span>%</span>
                  <span>5</span>
                </div>
              </Key>
              <Key code="Digit6" onClick={() => handleKeyClick("6", "Digit6")}>
                <div className="flex flex-col text-xs">
                  <span>^</span>
                  <span>6</span>
                </div>
              </Key>
              <Key code="Digit7" onClick={() => handleKeyClick("7", "Digit7")}>
                <div className="flex flex-col text-xs">
                  <span>&</span>
                  <span>7</span>
                </div>
              </Key>
              <Key code="Digit8" onClick={() => handleKeyClick("8", "Digit8")}>
                <div className="flex flex-col text-xs">
                  <span>*</span>
                  <span>8</span>
                </div>
              </Key>
              <Key code="Digit9" onClick={() => handleKeyClick("9", "Digit9")}>
                <div className="flex flex-col text-xs">
                  <span>(</span>
                  <span>9</span>
                </div>
              </Key>
              <Key code="Digit0" onClick={() => handleKeyClick("0", "Digit0")}>
                <div className="flex flex-col text-xs">
                  <span>)</span>
                  <span>0</span>
                </div>
              </Key>
              <Key code="Minus" onClick={() => handleKeyClick("-", "Minus")}>
                <div className="flex flex-col text-xs">
                  <span>_</span>
                  <span>-</span>
                </div>
              </Key>
              <Key code="Equal" onClick={() => handleKeyClick("=", "Equal")}>
                <div className="flex flex-col text-xs">
                  <span>+</span>
                  <span>=</span>
                </div>
              </Key>
              <Key
                code="Backspace"
                size="xl"
                onClick={() => handleKeyClick("Backspace", "Backspace")}
              >
                Backspace
              </Key>
            </KeyboardRow>

            {/* Tab + QWERTY Row */}
            <KeyboardRow>
              <Key
                code="Tab"
                size="lg"
                className="hidden md:flex"
                onClick={() => handleKeyClick("Tab", "Tab")}
              >
                Tab
              </Key>
              <Key code="KeyQ" onClick={() => handleKeyClick("q", "KeyQ")}>
                Q
              </Key>
              <Key code="KeyW" onClick={() => handleKeyClick("w", "KeyW")}>
                W
              </Key>
              <Key code="KeyE" onClick={() => handleKeyClick("e", "KeyE")}>
                E
              </Key>
              <Key code="KeyR" onClick={() => handleKeyClick("r", "KeyR")}>
                R
              </Key>
              <Key code="KeyT" onClick={() => handleKeyClick("t", "KeyT")}>
                T
              </Key>
              <Key code="KeyY" onClick={() => handleKeyClick("y", "KeyY")}>
                Y
              </Key>
              <Key code="KeyU" onClick={() => handleKeyClick("u", "KeyU")}>
                U
              </Key>
              <Key code="KeyI" onClick={() => handleKeyClick("i", "KeyI")}>
                I
              </Key>
              <Key code="KeyO" onClick={() => handleKeyClick("o", "KeyO")}>
                O
              </Key>
              <Key code="KeyP" onClick={() => handleKeyClick("p", "KeyP")}>
                P
              </Key>
              <Key
                code="BracketLeft"
                className="hidden md:flex"
                onClick={() => handleKeyClick("[", "BracketLeft")}
              >
                <div className="flex flex-col text-xs">
                  <span>{"{"}</span>
                  <span>[</span>
                </div>
              </Key>
              <Key
                code="BracketRight"
                className="hidden md:flex"
                onClick={() => handleKeyClick("]", "BracketRight")}
              >
                <div className="flex flex-col text-xs">
                  <span>{"}"}</span>
                  <span>]</span>
                </div>
              </Key>
              <Key
                code="Backslash"
                className="hidden md:flex"
                onClick={() => handleKeyClick("\\", "Backslash")}
              >
                <div className="flex flex-col text-xs">
                  <span>|</span>
                  <span>\</span>
                </div>
              </Key>
            </KeyboardRow>

            {/* Caps Lock + ASDF Row */}
            <KeyboardRow>
              <Key
                code="CapsLock"
                size="lg"
                className="hidden md:flex"
                onClick={() => handleKeyClick("CapsLock", "CapsLock")}
              >
                Caps
              </Key>
              <Key code="KeyA" onClick={() => handleKeyClick("a", "KeyA")}>
                A
              </Key>
              <Key code="KeyS" onClick={() => handleKeyClick("s", "KeyS")}>
                S
              </Key>
              <Key code="KeyD" onClick={() => handleKeyClick("d", "KeyD")}>
                D
              </Key>
              <Key code="KeyF" onClick={() => handleKeyClick("f", "KeyF")}>
                F
              </Key>
              <Key code="KeyG" onClick={() => handleKeyClick("g", "KeyG")}>
                G
              </Key>
              <Key code="KeyH" onClick={() => handleKeyClick("h", "KeyH")}>
                H
              </Key>
              <Key code="KeyJ" onClick={() => handleKeyClick("j", "KeyJ")}>
                J
              </Key>
              <Key code="KeyK" onClick={() => handleKeyClick("k", "KeyK")}>
                K
              </Key>
              <Key code="KeyL" onClick={() => handleKeyClick("l", "KeyL")}>
                L
              </Key>
              <Key
                code="Semicolon"
                className="hidden md:flex"
                onClick={() => handleKeyClick(";", "Semicolon")}
              >
                <div className="flex flex-col text-xs">
                  <span>:</span>
                  <span>;</span>
                </div>
              </Key>
              <Key
                code="Quote"
                className="hidden md:flex"
                onClick={() => handleKeyClick("'", "Quote")}
              >
                <div className="flex flex-col text-xs">
                  <span>&quot;</span>
                  <span>&apos;</span>
                </div>
              </Key>
              <Key
                code="Enter"
                size="xl"
                onClick={() => handleKeyClick("Enter", "Enter")}
              >
                Enter
              </Key>
            </KeyboardRow>

            {/* Shift + ZXCV Row */}
            <KeyboardRow>
              <Key
                code="ShiftLeft"
                size="xl"
                className="hidden md:flex"
                onClick={() => handleKeyClick("Shift", "ShiftLeft")}
              >
                Shift
              </Key>
              <Key code="KeyZ" onClick={() => handleKeyClick("z", "KeyZ")}>
                Z
              </Key>
              <Key code="KeyX" onClick={() => handleKeyClick("x", "KeyX")}>
                X
              </Key>
              <Key code="KeyC" onClick={() => handleKeyClick("c", "KeyC")}>
                C
              </Key>
              <Key code="KeyV" onClick={() => handleKeyClick("v", "KeyV")}>
                V
              </Key>
              <Key code="KeyB" onClick={() => handleKeyClick("b", "KeyB")}>
                B
              </Key>
              <Key code="KeyN" onClick={() => handleKeyClick("n", "KeyN")}>
                N
              </Key>
              <Key code="KeyM" onClick={() => handleKeyClick("m", "KeyM")}>
                M
              </Key>
              <Key code="Comma" onClick={() => handleKeyClick(",", "Comma")}>
                <div className="flex flex-col text-xs">
                  <span>{"<"}</span>
                  <span>,</span>
                </div>
              </Key>
              <Key code="Period" onClick={() => handleKeyClick(".", "Period")}>
                <div className="flex flex-col text-xs">
                  <span>{">"}</span>
                  <span>.</span>
                </div>
              </Key>
              <Key code="Slash" onClick={() => handleKeyClick("/", "Slash")}>
                <div className="flex flex-col text-xs">
                  <span>?</span>
                  <span>/</span>
                </div>
              </Key>
              <Key
                code="ShiftRight"
                size="xl"
                className="hidden md:flex"
                onClick={() => handleKeyClick("Shift", "ShiftRight")}
              >
                Shift
              </Key>
              <Key
                code="Backspace"
                size="md"
                className="md:hidden"
                onClick={() => handleKeyClick("Backspace", "Backspace")}
              >
                ⌫
              </Key>
            </KeyboardRow>

            {/* Bottom Row - Control keys + Space */}
            <KeyboardRow>
              <Key
                code="ControlLeft"
                size="lg"
                className="hidden md:flex"
                onClick={() => handleKeyClick("Control", "ControlLeft")}
              >
                Ctrl
              </Key>
              <Key
                code="MetaLeft"
                size="lg"
                className="hidden md:flex"
                onClick={() => handleKeyClick("Meta", "MetaLeft")}
              >
                ⌘
              </Key>
              <Key
                code="AltLeft"
                size="lg"
                className="hidden md:flex"
                onClick={() => handleKeyClick("Alt", "AltLeft")}
              >
                Alt
              </Key>
              <Key
                code="Space"
                className="w-24 md:w-40 lg:w-48 xl:w-56"
                onClick={() => handleKeyClick(" ", "Space")}
              >
                Space
              </Key>
              <Key
                code="AltRight"
                size="lg"
                className="hidden md:flex"
                onClick={() => handleKeyClick("Alt", "AltRight")}
              >
                Alt
              </Key>
              <Key
                code="MetaRight"
                size="lg"
                className="hidden md:flex"
                onClick={() => handleKeyClick("Meta", "MetaRight")}
              >
                ⌘
              </Key>
              <Key
                code="ControlRight"
                size="lg"
                className="hidden md:flex"
                onClick={() => handleKeyClick("Control", "ControlRight")}
              >
                Ctrl
              </Key>
            </KeyboardRow>

            {/* Mobile-specific row for common symbols */}
            <KeyboardRow className="md:hidden">
              <Key
                code="BracketLeft"
                onClick={() => handleKeyClick("[", "BracketLeft")}
              >
                [
              </Key>
              <Key
                code="BracketRight"
                onClick={() => handleKeyClick("]", "BracketRight")}
              >
                ]
              </Key>
              <Key
                code="Semicolon"
                onClick={() => handleKeyClick(";", "Semicolon")}
              >
                ;
              </Key>
              <Key code="Quote" onClick={() => handleKeyClick("'", "Quote")}>
                &apos;
              </Key>
              <Key
                code="Backslash"
                onClick={() => handleKeyClick("\\", "Backslash")}
              >
                \
              </Key>
              <Key code="Minus" onClick={() => handleKeyClick("-", "Minus")}>
                -
              </Key>
              <Key code="Equal" onClick={() => handleKeyClick("=", "Equal")}>
                =
              </Key>
            </KeyboardRow>
          </div>
        </div>
      </div>
    </div>
  )
}
