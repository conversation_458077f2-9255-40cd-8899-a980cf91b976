import { createOpenAI } from "@ai-sdk/openai"
import { createAnthropic } from "@ai-sdk/anthropic"
import { env } from "@/env"

export const openai = createOpenAI({
  apiKey: env.OPENAI_API_KEY,
})

export const anthropic = createAnthropic({
  apiKey: env.ANTHROPIC_API_KEY,
})

// Default model configurations
export const AI_MODELS = {
  openai: {
    chat: "gpt-4-turbo-preview",
    completion: "gpt-3.5-turbo-instruct",
  },
  anthropic: {
    chat: "claude-3-sonnet-20240229",
  },
} as const
