# QUY ĐỊNH SỬ DỤNG DỰ ÁN - BẮT BUỘC TUÂN THỦ

## 🚨 QUY TẮC BẮT BUỘC

### Package Manager
- **CHỈ sử dụng BUN** - Cấm npm/yarn/pnpm
- Commands: `bun install`, `bun dev`, `bun build`
- Lockfile: `bun.lock` (không được xóa)

### Database
- **CHỈ sử dụng Drizzle ORM** - Cấm raw SQL
- Luôn chạy `bun db:generate` sau khi sửa schema
- Luôn chạy `bun db:migrate` trước khi start app
- Schema phải có types: `export type User = typeof user.$inferSelect`

### Authentication
- **CHỈ sử dụng Better Auth** - Cấm auth khác
- Protected routes phải có middleware check
- Session phải được validate ở server-side
- Client-side dùng `useSession()` hook

### API
- **CHỈ sử dụng Hono với pattern đơn giản** - `const app = new Hono()`
- Mọi API phải type-safe với `AppType`
- Validation với `await c.req.json()` và manual validation
- Client phải dùng hooks từ `use-rpc.ts`
- Cấm sử dụng `@hono/zod-openapi` - quá phức tạp

### File Structure
- Components UI: `src/components/ui/` (shadcn/ui)
- Business logic: `src/components/` (custom components)
- API routes: `src/server/` (Hono routes)
- Database: `src/lib/db/` (schema + client)
- Hooks: `src/hooks/` (custom hooks)
- Store: `src/store/` (Zustand only)

### Code Quality
- **BẮT BUỘC** chạy `bun typecheck` trước commit
- **BẮT BUỘC** chạy `bun format` trước commit
- **BẮT BUỘC** chạy `bun lint` và fix errors
- Không được commit code có TypeScript errors

## 📋 CHECKLIST TRƯỚC KHI COMMIT

```bash
□ bun typecheck     # Không có TypeScript errors
□ bun format        # Code đã được format
□ bun lint          # Không có ESLint errors
□ bun db:generate   # Migrations đã được tạo (nếu có schema changes)
□ bun dev           # App chạy được local
```

## 🔒 QUY TẮC AUTHENTICATION

### Server-side (API routes)
```typescript
// BẮT BUỘC validate session
const session = await auth.api.getSession({ headers: request.headers })
if (!session) return new Response("Unauthorized", { status: 401 })
```

### Client-side (Components)
```typescript
// BẮT BUỘC sử dụng hook
const { data: session } = useSession()
if (!session) return <SignInForm />
```

### Middleware
```typescript
// BẮT BUỘC protect dashboard routes
if (!session && pathname.startsWith('/dashboard')) {
  return NextResponse.redirect('/auth/sign-in')
}
```

## 🔌 QUY TẮC HONO API

### Tạo Route
```typescript
// BẮT BUỘC: Pattern đơn giản
export const userRoute = new Hono()
  .get("/", async (c) => {
    const users = await db.select().from(user)
    return c.json({ users })
  })
  .post("/", async (c) => {
    const { name, email } = await c.req.json()
    // Manual validation nếu cần
    if (!name || !email) return c.json({ error: "Missing fields" }, 400)
    // ...
  })
```

### Client Usage
```typescript
// BẮT BUỘC: Sử dụng hooks từ use-rpc.ts
const { data, isLoading } = useUsers()
const createUser = useCreateUser()

// CẤMM: Gọi trực tiếp rpcClient trong component
```

### Error Handling
```typescript
// BẮT BUỘC: Handle errors properly
if (!response.ok) throw new Error("API call failed")
return response.json()
```

## 🗄️ QUY TẮC DATABASE

### Schema Definition
```typescript
// BẮT BUỘC: Export types
export const user = pgTable("user", { ... })
export type User = typeof user.$inferSelect
export type NewUser = typeof user.$inferInsert
```

### Queries
```typescript
// BẮT BUỘC: Sử dụng Drizzle syntax
const users = await db.select().from(user).where(eq(user.id, id))

// CẤM: Raw SQL
// const users = await db.execute(sql`SELECT * FROM user`)
```

### Migrations
```typescript
// BẮT BUỘC: Workflow
// 1. Sửa schema.ts
// 2. bun db:generate
// 3. Review migration file
// 4. bun db:migrate
```

## 🎨 QUY TẮC UI/COMPONENTS

### Component Structure
```
src/components/
├── ui/           # shadcn/ui components (KHÔNG SỬA)
├── forms/        # Form components
├── layout/       # Layout components
└── [feature]/    # Feature-specific components
```

### Styling
- **CHỈ sử dụng Tailwind CSS** - Cấm CSS modules/styled-components
- **CHỈ sử dụng shadcn/ui** cho base components
- Custom components phải extend từ shadcn/ui

### State Management
- **Local state**: React useState/useReducer
- **Global state**: Zustand store trong `src/store/`
- **Server state**: TanStack Query với RPC hooks

## ⚠️ CẢNH BÁO

### CẤMM TUYỆT ĐỐI
- ❌ Sử dụng npm/yarn thay vì bun
- ❌ Raw SQL queries thay vì Drizzle
- ❌ REST API thay vì RPC
- ❌ Custom auth thay vì Better Auth
- ❌ CSS-in-JS thay vì Tailwind
- ❌ Commit code có TypeScript errors
- ❌ Skip migrations khi có schema changes

### PHẠT KHI VI PHẠM
1. **Lần 1**: Warning + fix ngay
2. **Lần 2**: Code review reject
3. **Lần 3**: Revert commit + refactor

## 🚀 WORKFLOW CHUẨN

### Tạo Feature Mới
1. Tạo database schema (nếu cần)
2. Tạo RPC routes trong `src/server/`
3. Tạo hooks trong `src/hooks/use-rpc.ts`
4. Tạo components trong `src/components/`
5. Test với `bun dev`
6. Run checklist trước commit

### Debug Issues
1. Check terminal logs
2. `bun db:studio` cho database issues
3. `/api/health` cho API issues
4. Browser DevTools cho auth issues

---

**⚡ NHỚ: Tuân thủ 100% rules này để đảm bảo code quality và team consistency!**