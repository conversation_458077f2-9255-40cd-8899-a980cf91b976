import { authRoutes, DEFAULT_LOGIN_REDIRECT, protectedRoutes } from "@/router"
import { getSessionCookie } from "better-auth/cookies"
import { NextRequest, NextResponse } from "next/server"

export async function middleware(request: NextRequest) {
  const { nextUrl } = request

  const isProtectedRoutes =
    protectedRoutes.findIndex(value => nextUrl.pathname.startsWith(value)) != -1
  const isAuthRoutes =
    authRoutes.findIndex(value => nextUrl.pathname.startsWith(value)) != -1

  const sessionCookie = getSessionCookie(request)
  if (isProtectedRoutes) {
    if (!sessionCookie) {
      return NextResponse.redirect(new URL(DEFAULT_LOGIN_REDIRECT, request.url))
    }
  }

  if (isAuthRoutes) {
    if (sessionCookie) {
      return NextResponse.redirect(new URL("/", request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  // runtime: "nodejs",
  matcher: ["/((?!api|trpc|.*\\..*|_next).*)"],
}
