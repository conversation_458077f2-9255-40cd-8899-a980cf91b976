{"name": "nextjs-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "typecheck": "tsc --noEmit"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.5", "@ai-sdk/openai": "^2.0.16", "@ai-sdk/react": "^2.0.18", "@hono/node-server": "^1.19.0", "@hookform/resolvers": "^5.2.1", "@libsql/client": "^0.15.12", "@openrouter/ai-sdk-provider": "^1.1.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@react-email/components": "^0.5.0", "@react-spring/core": "^10.0.1", "@react-spring/three": "^10.0.1", "@react-three/drei": "^10.7.3", "@react-three/fiber": "^9.3.0", "@tanstack/react-query": "^5.85.3", "@tanstack/react-query-devtools": "^5.85.3", "@types/three": "^0.179.0", "ai": "^5.0.17", "better-auth": "^1.3.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "hono": "^4.9.2", "input-otp": "^1.4.2", "lucide-react": "^0.540.0", "next": "15.4.6", "next-themes": "^0.4.6", "pg": "^8.16.3", "radix-ui": "^1.4.3", "react": "19.1.0", "react-day-picker": "^9.9.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.4", "recharts": "2.15.4", "resend": "^6.0.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "three": "^0.179.1", "vaul": "^1.1.2", "zod": "^4.0.17", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@hono/zod-validator": "^0.7.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.6", "prettier": "^3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}