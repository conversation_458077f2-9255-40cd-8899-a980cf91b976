# Tài Liệu Cấu Trúc Dự Án

## Tổng Quan Dự Án

Đây là một ứng dụng web full-stack được xây dựng với Next.js 15, sử dụng Bun làm package manager thay vì npm. Dự án tích hợp AI SDK, authentication, database với PostgreSQL và Drizzle ORM.

## Công Nghệ Sử Dụng

### Core Technologies
- **Next.js 15.4.6** - React framework với App Router
- **React 19.1.0** - UI library
- **TypeScript 5** - Type safety
- **Bun** - Package manager và runtime (thay thế npm)
- **Tailwind CSS 4** - Styling framework

### Database & ORM
- **PostgreSQL** - Database chính
- **Drizzle ORM 0.44.4** - Type-safe ORM
- **@libsql/client** - Database client

### Authentication
- **Better Auth 1.3.7** - Authentication solution

### AI Integration
- **AI SDK 5.0.17** - Vercel AI SDK
- **@ai-sdk/anthropic** - Anthropic integration
- **@ai-sdk/openai** - OpenAI integration
- **@openrouter/ai-sdk-provider** - OpenRouter provider

### UI Components
- **Radix UI** - Headless UI components
- **Framer Motion** - Animation library
- **Lucide React** - Icon library
- **Sonner** - Toast notifications

### State Management
- **Zustand 5.0.8** - State management
- **TanStack Query 5.85.3** - Server state management

## Cấu Trúc Thư Mục Chi Tiết

```
nextjs-agent/
├── .env                    # Biến môi trường (không commit)
├── .env.example           # Template biến môi trường
├── .gitignore            # Git ignore rules
├── .kiro/                # Kiro AI assistant config
├── .next/                # Next.js build output
├── .prettierrc           # Prettier config
├── README.md             # Tài liệu dự án
├── bin/                  # Scripts thực thi
├── bun.lock             # Bun lockfile (thay thế package-lock.json)
├── components.json       # shadcn/ui config
├── docker-compose.yml    # Docker PostgreSQL setup
├── drizzle/             # Database migrations
│   ├── 0000_gifted_blue_shield.sql
│   └── meta/
├── drizzle.config.ts    # Drizzle ORM config
├── eslint.config.mjs    # ESLint config
├── next.config.ts       # Next.js config
├── package.json         # Dependencies và scripts
├── postcss.config.mjs   # PostCSS config
├── public/              # Static assets
│   ├── fonts/
│   └── *.svg
├── src/                 # Source code chính
│   ├── app/            # Next.js App Router
│   │   ├── (dashboard)/        # Route groups - dashboard routes
│   │   │   └── dashboard1/     # Nested dashboard page
│   │   ├── _component/         # Private components (không route)
│   │   │   ├── Keyboard3D.tsx  # 3D keyboard component
│   │   │   ├── KeyboardInput.tsx # Keyboard input handler
│   │   │   └── Terminal.tsx    # Terminal component
│   │   ├── api/               # API routes
│   │   │   ├── ai/           # AI endpoints
│   │   │   ├── auth/         # Auth endpoints (Better Auth)
│   │   │   └── rpc/          # RPC API endpoints
│   │   │       └── [...route]/
│   │   │           └── route.ts # Hono RPC handler
│   │   ├── auth/              # Authentication pages
│   │   │   ├── accept-invitation/
│   │   │   ├── forget-password/
│   │   │   ├── reset-password/
│   │   │   ├── sign-in/
│   │   │   ├── sign-up/
│   │   │   └── layout.tsx     # Auth layout
│   │   ├── dashboard/         # Dashboard pages
│   │   │   ├── layout.tsx     # Dashboard layout
│   │   │   └── page.tsx       # Dashboard home
│   │   ├── favicon.ico        # Favicon
│   │   ├── glitch.css        # Custom glitch effects
│   │   ├── globals.css       # Global styles
│   │   ├── layout.tsx        # Root layout
│   │   ├── not-found.tsx     # 404 page
│   │   └── page.tsx          # Home page
│   ├── components/           # Reusable components
│   │   ├── forms/           # Form components
│   │   │   ├── sign-in.tsx  # Sign in form
│   │   │   └── sign-up.tsx  # Sign up form
│   │   ├── layout/          # Layout components
│   │   │   ├── header.tsx   # Header component
│   │   │   └── sidebar.tsx  # Sidebar component
│   │   ├── originui/        # Custom UI components
│   │   │   └── password-input.tsx
│   │   ├── providers/       # React providers
│   │   │   └── query-provider.tsx # TanStack Query provider
│   │   ├── ui/             # shadcn/ui components (40+ components)
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── dialog.tsx
│   │   │   └── ... (tất cả UI components)
│   │   ├── app-sidebar.tsx  # Main sidebar
│   │   ├── nav-main.tsx     # Main navigation
│   │   ├── nav-projects.tsx # Projects navigation
│   │   ├── nav-user.tsx     # User navigation
│   │   ├── team-switcher.tsx # Team switcher
│   │   └── theme-provider.tsx # Theme provider
│   ├── db/                  # Database legacy (deprecated)
│   │   └── drizzle.ts      # Old database config
│   ├── hooks/              # Custom React hooks
│   │   ├── use-ai.ts       # AI integration hooks
│   │   ├── use-mobile.ts   # Mobile detection hook
│   │   └── use-rpc.ts      # RPC client hooks
│   ├── lib/                # Utility functions và configs
│   │   ├── ai/             # AI configuration
│   │   │   └── ai-config.ts # AI providers config
│   │   ├── db/             # Database layer
│   │   │   ├── drizzle.ts  # Drizzle client
│   │   │   ├── schema.ts   # Database schema
│   │   │   └── seed.ts     # Database seeding
│   │   ├── email/          # Email templates và config
│   │   │   ├── invitation.tsx    # Invitation email template
│   │   │   ├── resend.ts        # Resend config
│   │   │   └── reset-password.tsx # Reset password template
│   │   ├── rpc/            # RPC implementation
│   │   │   ├── rpc-client.ts # RPC client (Hono client)
│   │   │   └── rpc-server.ts # RPC server (Hono app)
│   │   ├── auth-client.ts  # Auth client utilities
│   │   ├── auth.ts         # Better Auth config
│   │   ├── keymap.ts       # Keyboard mappings
│   │   ├── rpc.ts          # RPC utilities (empty)
│   │   └── utils.ts        # General utilities
│   ├── server/             # Server-side API routes
│   │   ├── app-route.ts    # App-level routes (health check)
│   │   └── user-route.ts   # User management routes
│   ├── store/              # State management (Zustand)
│   │   └── useTypingStore.ts # Typing state store
│   ├── types/              # TypeScript type definitions
│   ├── env.ts              # Environment validation (Zod)
│   ├── middleware.ts       # Next.js middleware
│   └── router.ts           # Routing configuration
├── tsconfig.json           # TypeScript config
└── STRUCTURE.md           # Tài liệu này
```

## Scripts Quan Trọng

### Development
```bash
bun dev              # Chạy development server với Turbopack
bun build            # Build production
bun start            # Chạy production server
```

### Code Quality
```bash
bun lint             # ESLint check
bun format           # Format code với Prettier
bun format:check     # Check formatting
bun typecheck        # TypeScript type checking
```

### Database
```bash
bun db:generate      # Generate migrations
bun db:migrate       # Run migrations
bun db:push          # Push schema changes
bun db:studio        # Open Drizzle Studio
```

## Cài Đặt và Chạy Dự Án

### 1. Cài đặt dependencies
```bash
bun install
```

### 2. Cấu hình môi trường
```bash
cp .env.example .env
# Chỉnh sửa các biến môi trường trong .env
```

### 3. Chạy PostgreSQL với Docker
```bash
docker compose up -d
```

### 4. Chạy migrations
```bash
bun db:migrate
```

### 5. Chạy development server
```bash
bun dev
```

## Biến Môi Trường

Các biến môi trường quan trọng (xem `.env.example`):

- `DATABASE_URL` - PostgreSQL connection string
- `POSTGRES_USER` - Database user
- `POSTGRES_PASSWORD` - Database password  
- `POSTGRES_DB` - Database name
- API keys cho các AI providers (OpenAI, Anthropic, etc.)

## Đặc Điểm Nổi Bật

### 1. Sử Dụng Bun
- Thay thế npm/yarn với performance tốt hơn
- Lockfile: `bun.lock` thay vì `package-lock.json`
- Chạy scripts: `bun <script>` thay vì `npm run <script>`

### 2. Next.js App Router
- File-based routing với app directory
- Server Components mặc định
- Route groups với `(dashboard)`
- API routes trong `app/api/`

### 3. Type Safety
- TypeScript strict mode
- Drizzle ORM cho type-safe database queries
- Zod cho validation
- Environment variables validation

### 4. Modern UI
- Tailwind CSS 4 với PostCSS
- Radix UI components
- Dark/Light theme support
- Responsive design

### 5. AI Integration
- Multiple AI providers support
- Streaming responses
- React hooks cho AI interactions

## Database Schema

Database được quản lý bởi Drizzle ORM:
- Schema định nghĩa trong `src/lib/db/schema.ts`
- Migrations trong thư mục `drizzle/`
- Config trong `drizzle.config.ts`

## Authentication

Sử dụng Better Auth cho:
- User authentication
- Session management
- Protected routes
- Auth middleware

## Deployment

Dự án có thể deploy trên:
- Vercel (recommended cho Next.js)
- Docker containers
- Any Node.js hosting platform

## Authentication (Better Auth)

### Cấu hình Authentication

Better Auth được cấu hình trong `src/lib/auth.ts` với các tính năng:

- **Email/Password authentication**
- **Organization management** với invitations
- **Email verification** và password reset
- **Session management** với 7 ngày expiry
- **Google One Tap** (có thể enable)

### Sử dụng Authentication

#### 1. Server-side (trong API routes)
```typescript
import { auth } from "@/lib/auth"

// Lấy session hiện tại
const session = await auth.api.getSession({
  headers: request.headers
})

if (!session) {
  return new Response("Unauthorized", { status: 401 })
}

const user = session.user
```

#### 2. Client-side (trong components)
```typescript
import { useSession } from "@/lib/auth-client"

function MyComponent() {
  const { data: session, isPending } = useSession()
  
  if (isPending) return <div>Loading...</div>
  if (!session) return <div>Please sign in</div>
  
  return <div>Hello {session.user.name}</div>
}
```

#### 3. Middleware Protection
```typescript
// src/middleware.ts
import { auth } from "@/lib/auth"

export async function middleware(request: NextRequest) {
  const session = await auth.api.getSession({
    headers: request.headers
  })
  
  if (!session && request.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/auth/sign-in', request.url))
  }
}
```

### Database Schema cho Auth

```typescript
// Các bảng chính trong schema.ts
export const user = pgTable("user", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("email_verified").default(false),
  role: text("role").default("user"),
  // ... other fields
})

export const session = pgTable("session", {
  id: text("id").primaryKey(),
  userId: text("user_id").references(() => user.id),
  expiresAt: timestamp("expires_at").notNull(),
  // ... other fields
})

export const organization = pgTable("organization", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  slug: text("slug").unique(),
  // ... other fields
})
```

## API với Hono

### Kiến trúc API

Dự án sử dụng **Hono** với pattern đơn giản:

- **Server**: `src/lib/rpc/rpc-server.ts` - Hono app với routes
- **Client**: `src/lib/rpc/rpc-client.ts` - Type-safe client
- **Routes**: `src/server/` - Các route handlers
- **Hooks**: `src/hooks/use-rpc.ts` - React hooks cho API calls

### Cấu trúc Hono Server

```typescript
// src/lib/rpc/rpc-server.ts
import { Hono } from "hono"
import { cors } from "hono/cors"
import { logger } from "hono/logger"

const app = new Hono().basePath("/api")

// Middleware
app.use("*", cors())
app.use("*", logger())

// Routes
export const routes = app
  .route("/", appRoute)      // Health check
  .route("/user", userRoute) // User management
  .route("/ai", aiRoute)     // AI endpoints
  .route("/auth", authRoute) // Auth endpoints

export type AppType = typeof routes
```

### Tạo Hono Routes

#### 1. Tạo route handler
```typescript
// src/server/user-route.ts
import { Hono } from "hono"
import { db } from "@/lib/db/drizzle"
import { user } from "@/lib/db/schema"
import { randomUUID } from "crypto"

export const userRoute = new Hono()
  .get("/", async (c) => {
    const users = await db.select().from(user)
    return c.json({ users })
  })
  .get("/:id", async (c) => {
    const id = c.req.param("id")
    const userRows = await db.select().from(user).where(eq(user.id, id))
    if (userRows.length === 0) {
      return c.json({ error: "User not found" }, 404)
    }
    return c.json({ user: userRows[0] })
  })
  .post("/", async (c) => {
    const { name, email } = await c.req.json()
    // Manual validation
    if (!name || !email) {
      return c.json({ error: "Missing required fields" }, 400)
    }
    const newUser = await db.insert(user).values({
      id: randomUUID(),
      name,
      email,
    }).returning()
    return c.json({ user: newUser[0] }, 201)
  })
```

#### 2. Đăng ký route trong server
```typescript
// src/lib/rpc/rpc-server.ts
import { userRoute } from "@/server/user-route"

export const routes = app
  .route("/user", userRoute)
```

### Sử dụng API từ Client

#### 1. Hono Client Setup
```typescript
// src/lib/rpc/rpc-client.ts
import { hc } from "hono/client"
import { AppType } from "@/lib/rpc/rpc-server"

export const rpcClient = hc<AppType>(env.NEXT_PUBLIC_APP_URL!).api
```

#### 2. React Hooks cho API
```typescript
// src/hooks/use-rpc.ts
import { rpcClient } from "@/lib/rpc/rpc-client"
import { useQuery, useMutation } from "@tanstack/react-query"

export function useUsers() {
  return useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const response = await rpcClient.user.$get()
      if (!response.ok) throw new Error("Failed to fetch users")
      return response.json()
    },
  })
}

export function useCreateUser() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (data: { name: string; email: string }) => {
      const response = await rpcClient.user.$post({ json: data })
      if (!response.ok) throw new Error("Failed to create user")
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] })
    },
  })
}
```

#### 3. Sử dụng trong Components
```typescript
function UsersList() {
  const { data: users, isLoading } = useUsers()
  const createUser = useCreateUser()
  
  const handleCreate = () => {
    createUser.mutate({
      name: "John Doe",
      email: "<EMAIL>"
    })
  }
  
  if (isLoading) return <div>Loading...</div>
  
  return (
    <div>
      {users?.users.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
      <button onClick={handleCreate}>Create User</button>
    </div>
  )
}
```

## Database với Drizzle ORM

### Cấu hình Database

```typescript
// src/lib/db/drizzle.ts
import { drizzle } from "drizzle-orm/node-postgres"
import * as schema from "./schema"

export const db = drizzle(env.DATABASE_URL, { schema })
```

### Định nghĩa Schema

```typescript
// src/lib/db/schema.ts
import { pgTable, text, timestamp, boolean } from "drizzle-orm/pg-core"

export const user = pgTable("user", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("email_verified").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
})

// Export types
export type User = typeof user.$inferSelect
export type NewUser = typeof user.$inferInsert
```

### Sử dụng Database

#### 1. Basic Queries
```typescript
import { db } from "@/lib/db/drizzle"
import { user } from "@/lib/db/schema"
import { eq, and, or } from "drizzle-orm"

// Select all users
const users = await db.select().from(user)

// Select with conditions
const activeUsers = await db
  .select()
  .from(user)
  .where(eq(user.emailVerified, true))

// Select specific fields
const userEmails = await db
  .select({ email: user.email, name: user.name })
  .from(user)
```

#### 2. Insert Operations
```typescript
// Insert single user
const newUser = await db
  .insert(user)
  .values({
    id: randomUUID(),
    name: "John Doe",
    email: "<EMAIL>",
  })
  .returning()

// Insert multiple users
await db.insert(user).values([
  { id: "1", name: "User 1", email: "<EMAIL>" },
  { id: "2", name: "User 2", email: "<EMAIL>" },
])
```

#### 3. Update Operations
```typescript
// Update user
await db
  .update(user)
  .set({ 
    name: "Updated Name",
    updatedAt: new Date()
  })
  .where(eq(user.id, userId))

// Update with returning
const updatedUser = await db
  .update(user)
  .set({ emailVerified: true })
  .where(eq(user.email, email))
  .returning()
```

#### 4. Delete Operations
```typescript
// Delete user
await db
  .delete(user)
  .where(eq(user.id, userId))

// Delete with conditions
await db
  .delete(user)
  .where(and(
    eq(user.emailVerified, false),
    lt(user.createdAt, new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
  ))
```

#### 5. Joins và Relations
```typescript
// Join với session table
const usersWithSessions = await db
  .select({
    user: user,
    session: session,
  })
  .from(user)
  .leftJoin(session, eq(user.id, session.userId))

// Complex queries
const userStats = await db
  .select({
    userId: user.id,
    userName: user.name,
    sessionCount: count(session.id),
  })
  .from(user)
  .leftJoin(session, eq(user.id, session.userId))
  .groupBy(user.id, user.name)
```

### Database Migrations

```bash
# Generate migration từ schema changes
bun db:generate

# Apply migrations
bun db:migrate

# Push schema changes trực tiếp (development only)
bun db:push

# Open Drizzle Studio
bun db:studio
```

### Database Seeding

```typescript
// src/lib/db/seed.ts
import { db } from "./drizzle"
import { user } from "./schema"

async function seed() {
  await db.insert(user).values([
    {
      id: "1",
      name: "Admin User",
      email: "<EMAIL>",
      emailVerified: true,
      role: "admin",
    },
    // ... more seed data
  ])
}

// Run: bun run src/lib/db/seed.ts
```

## State Management

### Zustand Store

```typescript
// src/store/useTypingStore.ts
import { create } from 'zustand'

interface TypingState {
  isTyping: boolean
  currentText: string
  setTyping: (typing: boolean) => void
  setText: (text: string) => void
}

export const useTypingStore = create<TypingState>((set) => ({
  isTyping: false,
  currentText: '',
  setTyping: (typing) => set({ isTyping: typing }),
  setText: (text) => set({ currentText: text }),
}))
```

### TanStack Query

```typescript
// src/components/providers/query-provider.tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60 * 1000, // 1 minute
      retry: 1,
    },
  },
})

export function QueryProvider({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}
```

## Lưu Ý Quan Trọng

1. **Bun thay vì npm**: Luôn sử dụng `bun` commands thay vì `npm`
2. **Environment**: Đảm bảo cấu hình đúng `.env` file
3. **Database**: PostgreSQL phải chạy trước khi start app
4. **TypeScript**: Chạy `bun typecheck` trước khi commit
5. **Formatting**: Code được format tự động với Prettier
6. **RPC Type Safety**: Luôn export `AppType` từ RPC server để có type safety
7. **Database Migrations**: Chạy migrations sau mỗi schema change
8. **Auth Middleware**: Sử dụng auth middleware cho protected routes

## Hỗ Trợ

Để được hỗ trợ:
1. Kiểm tra logs trong terminal
2. Xem Drizzle Studio cho database issues: `bun db:studio`
3. Check Next.js documentation
4. Review environment variables
5. Test API endpoints với health check: `/api/health`
6. Kiểm tra auth session trong browser DevTools