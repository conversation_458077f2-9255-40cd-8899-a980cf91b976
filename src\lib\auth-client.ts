import { createAuthClient } from "better-auth/react"
import { env } from "@/env"
import {
  // oneTapClient,
  organizationClient,
} from "better-auth/client/plugins"

export const authClient = createAuthClient({
  baseURL: env.NEXT_PUBLIC_APP_URL,
  plugins: [
    organizationClient(),
    // oneTapClient({
    //     clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
    //     promptOptions: {
    //         maxAttempts: 1,
    //     },
    // }),
  ],
})

export const { signIn, signUp, signOut, useSession, getSession, organization } =
  authClient
