#!/usr/bin/env bash
set -euo pipefail

FILE=".env"
FORCE=0
PRINT=0

while [ $# -gt 0 ]; do
  case "$1" in
    --force) FORCE=1; shift;;
    --print) PRINT=1; shift;;
    *) echo "Unknown arg: $1"; exit 1;;
  esac
done

gen_secret() {
  if command -v openssl >/dev/null 2>&1; then
    openssl rand -hex 32
  else
    node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
  fi
}

replace_or_append() {
  local key=$1
  local val=$2
  if [ -f "$FILE" ] && grep -qE "^${key}=" "$FILE"; then
    if [ "$FORCE" -eq 1 ]; then
      sed -i.bak -E "s~^${key}=.*~${key}=${val}~" "$FILE"
    fi
  else
    echo "${key}=${val}" >> "$FILE"
  fi
}

BETTER_AUTH_SECRET_VAL=$(gen_secret)
POSTGRES_PASSWORD_VAL=$(gen_secret)

if [ "$PRINT" -eq 1 ]; then
  echo "BETTER_AUTH_SECRET=$BETTER_AUTH_SECRET_VAL"
  echo "POSTGRES_PASSWORD=$POSTGRES_PASSWORD_VAL"
  exit 0
fi

touch "$FILE"
replace_or_append "BETTER_AUTH_SECRET" "$BETTER_AUTH_SECRET_VAL"
replace_or_append "POSTGRES_PASSWORD" "$POSTGRES_PASSWORD_VAL"
replace_or_append "POSTGRES_USER" "postgres"
replace_or_append "POSTGRES_DB" "postgres"

if ! grep -qE "^DATABASE_URL=" "$FILE"; then
  PUSER=$(grep -E '^POSTGRES_USER=' "$FILE" | cut -d= -f2)
  PPASS=$(grep -E '^POSTGRES_PASSWORD=' "$FILE" | cut -d= -f2)
  PDB=$(grep -E '^POSTGRES_DB=' "$FILE" | cut -d= -f2)
  echo "DATABASE_URL=postgres://$PUSER:$PPASS@localhost:5432/$PDB" >> "$FILE"
fi

echo "Wrote secrets to $FILE (use --force to overwrite existing values)."