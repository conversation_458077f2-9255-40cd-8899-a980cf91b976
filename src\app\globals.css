@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));
:root {
  --background: oklch(0.97 0 0);
  --foreground: oklch(0.3729 0.0306 259.7328);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.3729 0.0306 259.7328);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.3729 0.0306 259.7328);
  --primary: oklch(0.527 0.154 150.069);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.922 0 0);
  --secondary-foreground: oklch(0.4461 0.0263 256.8018);
  --muted: oklch(0.967 0.0029 264.5419);
  --muted-foreground: oklch(0.551 0.0234 264.3637);
  --accent: oklch(0.9505 0.0507 163.0508);
  --accent-foreground: oklch(0.3729 0.0306 259.7328);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9276 0.0058 264.5313);
  --input: oklch(0.9276 0.0058 264.5313);
  --ring: oklch(0.7227 0.192 149.5793);
  --chart-1: oklch(0.7227 0.192 149.5793);
  --chart-2: oklch(0.6959 0.1491 162.4796);
  --chart-3: oklch(0.596 0.1274 163.2254);
  --chart-4: oklch(0.5081 0.1049 165.6121);
  --chart-5: oklch(0.4318 0.0865 166.9128);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.3729 0.0306 259.7328);
  --sidebar-primary: oklch(0.7227 0.192 149.5793);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.9505 0.0507 163.0508);
  --sidebar-accent-foreground: oklch(0.3729 0.0306 259.7328);
  --sidebar-border: oklch(0.9276 0.0058 264.5313);
  --sidebar-ring: oklch(0.7227 0.192 149.5793);
  --font-sans: DM Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 3px 3px 0px 0px hsl(223.8136 0.0001% 89.8161% / 0.5);
  --shadow-xs: 3px 3px 0px 0px hsl(223.8136 0.0001% 89.8161% / 0.5);
  --shadow-sm:
    3px 3px 0px 0px hsl(223.8136 0.0001% 89.8161% / 1),
    3px 1px 2px -1px hsl(223.8136 0.0001% 89.8161% / 1);
  --shadow:
    3px 3px 0px 0px hsl(223.8136 0.0001% 89.8161% / 1),
    3px 1px 2px -1px hsl(223.8136 0.0001% 89.8161% / 1);
  --shadow-md:
    3px 3px 0px 0px hsl(223.8136 0.0001% 89.8161% / 1),
    3px 2px 4px -1px hsl(223.8136 0.0001% 89.8161% / 1);
  --shadow-lg:
    3px 3px 0px 0px hsl(223.8136 0.0001% 89.8161% / 1),
    3px 4px 6px -1px hsl(223.8136 0.0001% 89.8161% / 1);
  --shadow-xl:
    3px 3px 0px 0px hsl(223.8136 0.0001% 89.8161% / 1),
    3px 8px 10px -1px hsl(223.8136 0.0001% 89.8161% / 1);
  --shadow-2xl: 3px 3px 0px 0px hsl(223.8136 0.0001% 89.8161% / 2.5);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2077 0.0398 265.7549);
  --foreground: oklch(0.8717 0.0093 258.3382);
  --card: oklch(0.2795 0.0368 260.031);
  --card-foreground: oklch(0.8717 0.0093 258.3382);
  --popover: oklch(0.2795 0.0368 260.031);
  --popover-foreground: oklch(0.8717 0.0093 258.3382);
  --primary: oklch(0.7729 0.1535 163.2231);
  --primary-foreground: oklch(0.2077 0.0398 265.7549);
  --secondary: oklch(0.3351 0.0331 260.912);
  --secondary-foreground: oklch(0.7118 0.0129 286.0665);
  --muted: oklch(0.2795 0.0368 260.031);
  --muted-foreground: oklch(0.551 0.0234 264.3637);
  --accent: oklch(0.3729 0.0306 259.7328);
  --accent-foreground: oklch(0.7118 0.0129 286.0665);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(0.2077 0.0398 265.7549);
  --border: oklch(0.4461 0.0263 256.8018);
  --input: oklch(0.4461 0.0263 256.8018);
  --ring: oklch(0.7729 0.1535 163.2231);
  --chart-1: oklch(0.7729 0.1535 163.2231);
  --chart-2: oklch(0.7845 0.1325 181.912);
  --chart-3: oklch(0.7227 0.192 149.5793);
  --chart-4: oklch(0.6959 0.1491 162.4796);
  --chart-5: oklch(0.596 0.1274 163.2254);
  --sidebar: oklch(0.2795 0.0368 260.031);
  --sidebar-foreground: oklch(0.8717 0.0093 258.3382);
  --sidebar-primary: oklch(0.7729 0.1535 163.2231);
  --sidebar-primary-foreground: oklch(0.2077 0.0398 265.7549);
  --sidebar-accent: oklch(0.3729 0.0306 259.7328);
  --sidebar-accent-foreground: oklch(0.7118 0.0129 286.0665);
  --sidebar-border: oklch(0.4461 0.0263 256.8018);
  --sidebar-ring: oklch(0.7729 0.1535 163.2231);
  --font-sans: DM Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 3px 3px 0px 0px hsl(0 0% 0% / 0.5);
  --shadow-xs: 3px 3px 0px 0px hsl(0 0% 0% / 0.5);
  --shadow-sm:
    3px 3px 0px 0px hsl(0 0% 0% / 1), 3px 1px 2px -1px hsl(0 0% 0% / 1);
  --shadow: 3px 3px 0px 0px hsl(0 0% 0% / 1), 3px 1px 2px -1px hsl(0 0% 0% / 1);
  --shadow-md:
    3px 3px 0px 0px hsl(0 0% 0% / 1), 3px 2px 4px -1px hsl(0 0% 0% / 1);
  --shadow-lg:
    3px 3px 0px 0px hsl(0 0% 0% / 1), 3px 4px 6px -1px hsl(0 0% 0% / 1);
  --shadow-xl:
    3px 3px 0px 0px hsl(0 0% 0% / 1), 3px 8px 10px -1px hsl(0 0% 0% / 1);
  --shadow-2xl: 3px 3px 0px 0px hsl(0 0% 0% / 2.5);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}
@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}
