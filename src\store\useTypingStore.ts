import { create } from "zustand"

export interface TypingState {
  currentLine: string
  history: string[]
  caretPosition: number
  pressedKeys: Set<string>
  isTyping: boolean
  isWaitingForResponse: boolean
}

interface TypingActions {
  setCurrentLine: (line: string) => void
  addToHistory: (line: string) => void
  setCaretPosition: (position: number) => void
  addPressedKey: (key: string) => void
  removePressedKey: (key: string) => void
  clearPressedKeys: () => void
  setIsTyping: (typing: boolean) => void
  setIsWaitingForResponse: (waiting: boolean) => void
  handleKeyPress: (key: string, code: string, shiftKey?: boolean) => void
  handleBackspace: () => void
  handleEnter: () => void
  handleArrowLeft: () => void
  handleArrowRight: () => void
  handleTab: () => void
  handleSpecialKey: (key: string) => void
  clearTerminal: () => void
}

type TypingStore = TypingState & TypingActions

const WELCOME_MESSAGE = [
  "Welcome to NextJS AI Terminal 🚀",
  "Type anything to see the 3D keyboard in action...",
  "",
]

// Fake responses for the bot
const BOT_RESPONSES = [
  "Tôi hiểu bạn muốn biết về điều đó! 🤔",
  "Đây là một câu trả lời thông minh từ AI bot! ✨",
  "Hmm, để tôi suy nghĩ... Có vẻ như bạn đang hỏi về một chủ đề thú vị! 🧠",
  "Dựa trên những gì bạn nói, tôi nghĩ rằng... 💭",
  "Cảm ơn bạn đã hỏi! Đây là phản hồi của tôi: 🎯",
  "Tôi đang học hỏi từ cuộc trò chuyện này! 📚",
  "Đó là một câu hỏi hay! Hãy để tôi trả lời... 🚀",
]

function getRandomBotResponse(): string {
  return BOT_RESPONSES[Math.floor(Math.random() * BOT_RESPONSES.length)]
}

export const useTypingStore = create<TypingStore>((set, get) => ({
  // State
  currentLine: "",
  history: WELCOME_MESSAGE,
  caretPosition: 0,
  pressedKeys: new Set(),
  isTyping: false,
  isWaitingForResponse: false,

  // Actions
  setCurrentLine: (line: string) => set({ currentLine: line }),

  addToHistory: (line: string) =>
    set(state => ({
      history: [...state.history, line],
    })),

  setCaretPosition: (position: number) => set({ caretPosition: position }),

  addPressedKey: (key: string) =>
    set(state => ({
      pressedKeys: new Set([...state.pressedKeys, key]),
    })),

  removePressedKey: (key: string) =>
    set(state => {
      const newKeys = new Set(state.pressedKeys)
      newKeys.delete(key)
      return { pressedKeys: newKeys }
    }),

  clearPressedKeys: () => set({ pressedKeys: new Set() }),

  setIsTyping: (typing: boolean) => set({ isTyping: typing }),

  setIsWaitingForResponse: (waiting: boolean) =>
    set({ isWaitingForResponse: waiting }),

  handleKeyPress: (key: string, code: string, shiftKey = false) => {
    console.log("🎹 handleKeyPress called:", { key, code, shiftKey })
    const state = get()

    // Handle special characters mapping
    let charToAdd = key

    // Handle special key mappings for symbols
    if (shiftKey) {
      const shiftMap: { [key: string]: string } = {
        "1": "!",
        "2": "@",
        "3": "#",
        "4": "$",
        "5": "%",
        "6": "^",
        "7": "&",
        "8": "*",
        "9": "(",
        "0": ")",
        "-": "_",
        "=": "+",
        "[": "{",
        "]": "}",
        "\\": "|",
        ";": ":",
        "'": '"',
        ",": "<",
        ".": ">",
        "/": "?",
        "`": "~",
      }
      charToAdd = shiftMap[key] || key.toUpperCase()
    } else if (key.length === 1) {
      charToAdd = key.toLowerCase()
    }

    // Add character to current line
    if (charToAdd === "\t") {
      // Handle tab as 4 spaces
      charToAdd = "    "
    }

    if (charToAdd.length >= 1) {
      const newLine =
        state.currentLine.substring(0, state.caretPosition) +
        charToAdd +
        state.currentLine.substring(state.caretPosition)

      set({
        currentLine: newLine,
        caretPosition: state.caretPosition + charToAdd.length,
      })
    }
  },

  handleBackspace: () => {
    const state = get()
    if (state.caretPosition > 0) {
      const newLine =
        state.currentLine.substring(0, state.caretPosition - 1) +
        state.currentLine.substring(state.caretPosition)

      set({
        currentLine: newLine,
        caretPosition: state.caretPosition - 1,
      })
    }
  },

  handleEnter: () => {
    const state = get()
    if (state.currentLine.trim()) {
      const prompt = `$ ${state.currentLine}`

      set({
        history: [...state.history, prompt],
        currentLine: "",
        caretPosition: 0,
        isWaitingForResponse: true,
      })

      // Simulate AI bot response after 3 seconds
      setTimeout(() => {
        const botResponse = getRandomBotResponse()
        set(currentState => ({
          history: [...currentState.history, `🤖 AI: ${botResponse}`],
          isWaitingForResponse: false,
        }))
      }, 3000)
    } else {
      // Empty line, just add prompt
      set({
        history: [...state.history, "$ "],
        currentLine: "",
        caretPosition: 0,
      })
    }
  },

  handleArrowLeft: () => {
    const state = get()
    if (state.caretPosition > 0) {
      set({ caretPosition: state.caretPosition - 1 })
    }
  },

  handleArrowRight: () => {
    const state = get()
    if (state.caretPosition < state.currentLine.length) {
      set({ caretPosition: state.caretPosition + 1 })
    }
  },

  handleTab: () => {
    const state = get()
    const tabSpaces = "    " // 4 spaces for tab
    const newLine =
      state.currentLine.substring(0, state.caretPosition) +
      tabSpaces +
      state.currentLine.substring(state.caretPosition)

    set({
      currentLine: newLine,
      caretPosition: state.caretPosition + 4,
    })
  },

  handleSpecialKey: (key: string) => {
    // Handle special keys like function keys, etc.
    console.log(`Special key pressed: ${key}`)
    // Could add specific functionality for different special keys
    switch (key) {
      case "Escape":
        // Clear current line on escape
        set({ currentLine: "", caretPosition: 0 })
        break
      case "CapsLock":
        // Toggle caps lock state (could implement if needed)
        console.log("Caps Lock toggled")
        break
      default:
        console.log(`Unhandled special key: ${key}`)
    }
  },

  clearTerminal: () =>
    set({
      currentLine: "",
      history: WELCOME_MESSAGE,
      caretPosition: 0,
    }),
}))
