import { drizzle } from "drizzle-orm/node-postgres"
import { env } from "@/env"
import * as schema from "./schema"

/**
 * Drizzle with Postgres (postgres package)
 * Use DATABASE_URL with postgres:// scheme.
 * This file must run on the server only.
 */
if (typeof window !== "undefined") {
  throw new Error(
    "src/lib/db/drizzle.ts: Attempted to initialize database on the client. This module is server-only."
  )
}

if (!env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL is not defined. Please set DATABASE_URL in your environment."
  )
}

export const db = drizzle(env.DATABASE_URL, {
  schema,
})

export type Database = typeof db

export * from "./schema"
