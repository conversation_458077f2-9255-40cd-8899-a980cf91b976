"use client"
// import { authClient } from "@/lib/auth-client";
import { useRouter } from "next/navigation"
import { useEffect } from "react"
// import { toast } from "sonner";

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const router = useRouter()
  useEffect(() => {
    // authClient.oneTap({
    //     fetchOptions: {
    //         onError: ({ error }) => {
    //             toast.error(error.message || "An error occurred");
    //         },
    //         onSuccess: () => {
    //             toast.success("Successfully signed in");
    //             router.push("/dashboard");
    //         },
    //     },
    // });
  }, [router])

  return children
}
