export interface KeyboardKey {
  key: string
  displayKey: string
  code: string
  position: [number, number, number] // [x, y, z]
  size: [number, number, number] // [width, height, depth]
  type: "letter" | "number" | "special" | "modifier"
}

export const KEYBOARD_LAYOUT: KeyboardKey[] = [
  // Number row
  {
    key: "`",
    displayKey: "~",
    code: "Backquote",
    position: [-9, 0, 3],
    size: [1, 0.2, 1],
    type: "special",
  },
  {
    key: "1",
    displayKey: "!",
    code: "Digit1",
    position: [-8, 0, 3],
    size: [1, 0.2, 1],
    type: "number",
  },
  {
    key: "2",
    displayKey: "@",
    code: "Digit2",
    position: [-7, 0, 3],
    size: [1, 0.2, 1],
    type: "number",
  },
  {
    key: "3",
    displayKey: "#",
    code: "Digit3",
    position: [-6, 0, 3],
    size: [1, 0.2, 1],
    type: "number",
  },
  {
    key: "4",
    displayKey: "$",
    code: "Digit4",
    position: [-5, 0, 3],
    size: [1, 0.2, 1],
    type: "number",
  },
  {
    key: "5",
    displayKey: "%",
    code: "Digit5",
    position: [-4, 0, 3],
    size: [1, 0.2, 1],
    type: "number",
  },
  {
    key: "6",
    displayKey: "^",
    code: "Digit6",
    position: [-3, 0, 3],
    size: [1, 0.2, 1],
    type: "number",
  },
  {
    key: "7",
    displayKey: "&",
    code: "Digit7",
    position: [-2, 0, 3],
    size: [1, 0.2, 1],
    type: "number",
  },
  {
    key: "8",
    displayKey: "*",
    code: "Digit8",
    position: [-1, 0, 3],
    size: [1, 0.2, 1],
    type: "number",
  },
  {
    key: "9",
    displayKey: "(",
    code: "Digit9",
    position: [0, 0, 3],
    size: [1, 0.2, 1],
    type: "number",
  },
  {
    key: "0",
    displayKey: ")",
    code: "Digit0",
    position: [1, 0, 3],
    size: [1, 0.2, 1],
    type: "number",
  },
  {
    key: "-",
    displayKey: "_",
    code: "Minus",
    position: [2, 0, 3],
    size: [1, 0.2, 1],
    type: "special",
  },
  {
    key: "=",
    displayKey: "+",
    code: "Equal",
    position: [3, 0, 3],
    size: [1, 0.2, 1],
    type: "special",
  },
  {
    key: "Backspace",
    displayKey: "⌫",
    code: "Backspace",
    position: [4.5, 0, 3],
    size: [2, 0.2, 1],
    type: "special",
  },

  // QWERTY row
  {
    key: "Tab",
    displayKey: "⇥",
    code: "Tab",
    position: [-9, 0, 2],
    size: [1.5, 0.2, 1],
    type: "modifier",
  },
  {
    key: "q",
    displayKey: "Q",
    code: "KeyQ",
    position: [-7.25, 0, 2],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "w",
    displayKey: "W",
    code: "KeyW",
    position: [-6.25, 0, 2],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "e",
    displayKey: "E",
    code: "KeyE",
    position: [-5.25, 0, 2],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "r",
    displayKey: "R",
    code: "KeyR",
    position: [-4.25, 0, 2],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "t",
    displayKey: "T",
    code: "KeyT",
    position: [-3.25, 0, 2],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "y",
    displayKey: "Y",
    code: "KeyY",
    position: [-2.25, 0, 2],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "u",
    displayKey: "U",
    code: "KeyU",
    position: [-1.25, 0, 2],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "i",
    displayKey: "I",
    code: "KeyI",
    position: [-0.25, 0, 2],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "o",
    displayKey: "O",
    code: "KeyO",
    position: [0.75, 0, 2],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "p",
    displayKey: "P",
    code: "KeyP",
    position: [1.75, 0, 2],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "[",
    displayKey: "{",
    code: "BracketLeft",
    position: [2.75, 0, 2],
    size: [1, 0.2, 1],
    type: "special",
  },
  {
    key: "]",
    displayKey: "}",
    code: "BracketRight",
    position: [3.75, 0, 2],
    size: [1, 0.2, 1],
    type: "special",
  },
  {
    key: "\\",
    displayKey: "|",
    code: "Backslash",
    position: [4.875, 0, 2],
    size: [1.25, 0.2, 1],
    type: "special",
  },

  // ASDF row
  {
    key: "CapsLock",
    displayKey: "⇪",
    code: "CapsLock",
    position: [-9, 0, 1],
    size: [1.75, 0.2, 1],
    type: "modifier",
  },
  {
    key: "a",
    displayKey: "A",
    code: "KeyA",
    position: [-7.125, 0, 1],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "s",
    displayKey: "S",
    code: "KeyS",
    position: [-6.125, 0, 1],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "d",
    displayKey: "D",
    code: "KeyD",
    position: [-5.125, 0, 1],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "f",
    displayKey: "F",
    code: "KeyF",
    position: [-4.125, 0, 1],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "g",
    displayKey: "G",
    code: "KeyG",
    position: [-3.125, 0, 1],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "h",
    displayKey: "H",
    code: "KeyH",
    position: [-2.125, 0, 1],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "j",
    displayKey: "J",
    code: "KeyJ",
    position: [-1.125, 0, 1],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "k",
    displayKey: "K",
    code: "KeyK",
    position: [-0.125, 0, 1],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "l",
    displayKey: "L",
    code: "KeyL",
    position: [0.875, 0, 1],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: ";",
    displayKey: ":",
    code: "Semicolon",
    position: [1.875, 0, 1],
    size: [1, 0.2, 1],
    type: "special",
  },
  {
    key: "'",
    displayKey: '"',
    code: "Quote",
    position: [2.875, 0, 1],
    size: [1, 0.2, 1],
    type: "special",
  },
  {
    key: "Enter",
    displayKey: "↵",
    code: "Enter",
    position: [4.4375, 0, 1],
    size: [2.125, 0.2, 1],
    type: "special",
  },

  // ZXCV row
  {
    key: "Shift",
    displayKey: "⇧",
    code: "ShiftLeft",
    position: [-9, 0, 0],
    size: [2.25, 0.2, 1],
    type: "modifier",
  },
  {
    key: "z",
    displayKey: "Z",
    code: "KeyZ",
    position: [-6.875, 0, 0],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "x",
    displayKey: "X",
    code: "KeyX",
    position: [-5.875, 0, 0],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "c",
    displayKey: "C",
    code: "KeyC",
    position: [-4.875, 0, 0],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "v",
    displayKey: "V",
    code: "KeyV",
    position: [-3.875, 0, 0],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "b",
    displayKey: "B",
    code: "KeyB",
    position: [-2.875, 0, 0],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "n",
    displayKey: "N",
    code: "KeyN",
    position: [-1.875, 0, 0],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: "m",
    displayKey: "M",
    code: "KeyM",
    position: [-0.875, 0, 0],
    size: [1, 0.2, 1],
    type: "letter",
  },
  {
    key: ",",
    displayKey: "<",
    code: "Comma",
    position: [0.125, 0, 0],
    size: [1, 0.2, 1],
    type: "special",
  },
  {
    key: ".",
    displayKey: ">",
    code: "Period",
    position: [1.125, 0, 0],
    size: [1, 0.2, 1],
    type: "special",
  },
  {
    key: "/",
    displayKey: "?",
    code: "Slash",
    position: [2.125, 0, 0],
    size: [1, 0.2, 1],
    type: "special",
  },
  {
    key: "Shift",
    displayKey: "⇧",
    code: "ShiftRight",
    position: [4.125, 0, 0],
    size: [2.75, 0.2, 1],
    type: "modifier",
  },

  // Space row
  {
    key: "Control",
    displayKey: "⌃",
    code: "ControlLeft",
    position: [-9, 0, -1],
    size: [1.25, 0.2, 1],
    type: "modifier",
  },
  {
    key: "Alt",
    displayKey: "⌥",
    code: "AltLeft",
    position: [-7.625, 0, -1],
    size: [1.25, 0.2, 1],
    type: "modifier",
  },
  {
    key: "Meta",
    displayKey: "⌘",
    code: "MetaLeft",
    position: [-6.25, 0, -1],
    size: [1.25, 0.2, 1],
    type: "modifier",
  },
  {
    key: " ",
    displayKey: "Space",
    code: "Space",
    position: [-1.75, 0, -1],
    size: [6.25, 0.2, 1],
    type: "special",
  },
  {
    key: "Meta",
    displayKey: "⌘",
    code: "MetaRight",
    position: [2.75, 0, -1],
    size: [1.25, 0.2, 1],
    type: "modifier",
  },
  {
    key: "Alt",
    displayKey: "⌥",
    code: "AltRight",
    position: [4.125, 0, -1],
    size: [1.25, 0.2, 1],
    type: "modifier",
  },
  {
    key: "Control",
    displayKey: "⌃",
    code: "ControlRight",
    position: [5.5, 0, -1],
    size: [1.25, 0.2, 1],
    type: "modifier",
  },
]

// Helper function to get key by code
export const getKeyByCode = (code: string): KeyboardKey | undefined => {
  return KEYBOARD_LAYOUT.find(key => key.code === code)
}

// Helper function to get key by key value
export const getKeyByValue = (keyValue: string): KeyboardKey | undefined => {
  return KEYBOARD_LAYOUT.find(
    key => key.key.toLowerCase() === keyValue.toLowerCase()
  )
}

// Color scheme for different key types
export const KEY_COLORS = {
  letter: "#f8f9fa",
  number: "#e9ecef",
  special: "#dee2e6",
  modifier: "#ced4da",
}

export const KEY_PRESSED_COLOR = "#007bff"
export const KEY_EMISSIVE_COLOR = "#0056b3"
