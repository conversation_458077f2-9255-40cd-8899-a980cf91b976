import { db } from "@/lib/db/drizzle"
import { user } from "@/lib/db/schema"
import { Hono } from "hono"
import { randomUUID } from "crypto"
import { eq } from "drizzle-orm"

export const userRoute = new Hono()
  .get("/", async c => {
    const allUsers = await db.select().from(user)
    return c.json({ users: allUsers })
  })
  .get("/:id", async c => {
    const id = c.req.param("id")
    const userRows = await db.select().from(user).where(eq(user.id, id))
    if (userRows.length === 0) {
      return c.json({ error: "User not found" }, 404)
    }
    return c.json({ user: userRows[0] })
  })
  .post("/", async c => {
    const { name, email } = await c.req.json()
    const newUser = await db
      .insert(user)
      .values({
        id: randomUUID(),
        name,
        email,
      })
      .returning()
    return c.json({ user: newUser[0] }, 201)
  })
